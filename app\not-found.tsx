'use client';

import { BookOpen, FileSearch, Home } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {

    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative flex min-h-[80vh] flex-col items-center justify-center overflow-hidden px-4 py-10">
      {/* Background decorative elements */}
      <div className="-z-10 absolute inset-0 overflow-hidden">
        <div
          aria-hidden="true"
          className="-z-10 -translate-x-1/2 xl:-top-6 absolute top-0 left-1/2 blur-3xl"
        >
          <div
            className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-primary/30 to-primary-foreground/30 opacity-30"
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
          />
        </div>
      </div>

      <div className="relative z-10 mx-auto flex max-w-5xl flex-col items-center gap-8 md:flex-row md:gap-12">
        {/* Image */}
        <div
          className={`relative transition-all duration-700 ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-8 opacity-0'}`}
        >
          <div className="relative">
            <div className="-inset-0.5 absolute rounded-2xl bg-gradient-to-r from-primary/20 to-primary/40 opacity-75 blur-xl" />
            <div className="relative overflow-hidden rounded-2xl border border-primary/10 bg-background/50 backdrop-blur-sm">
              <Image
                alt="404 Illustration"
                className="rounded-2xl object-cover"
                height={400}
                priority
                src="/images/404-right.webp"
                style={{ aspectRatio: '400/400', objectFit: 'cover' }}
                width={400}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div
          className={`text-center transition-all delay-300 duration-700 md:text-left ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
        >
          <div className="mb-4 inline-flex items-center justify-center rounded-full bg-primary/10 p-2 text-primary">
            <FileSearch className="h-6 w-6" />
          </div>

          <h1 className="mb-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text font-bold text-7xl text-transparent tracking-tight">
            404
          </h1>
          <h2 className="mb-4 font-semibold text-3xl">Page Not Found</h2>

          {/* Enhanced Bible Quote Section */}
          <div className="mb-8 max-w-md">
            <div className="flex items-start gap-3">
              <BookOpen className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
              <div>
                <p className="mb-2 text-lg italic">
                  &quot;For we live by faith, not by sight.&quot;
                </p>
                <p className="text-right text-muted-foreground text-sm">
                  — 2 Corinthians 5:7
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap justify-center gap-4 md:justify-start">
            <Button
              asChild
              className="gap-2 rounded-md px-6 shadow-lg transition-all hover:shadow-xl"
              size="lg"
            >
              <Link href="/">
                <Home className="h-5 w-5" />
                Back to Home
              </Link>
            </Button>

            {/*<Button variant="outline" asChild size="lg" className="rounded-md px-6 gap-2 border-primary/20 hover:bg-primary/5">
                    <Link href="/help">
                      <HelpCircle className="h-5 w-5" />
                      Help Center
                    </Link>
                  </Button>*/}
          </div>
        </div>
      </div>
    </div>
  );
}
