// External dependencies
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText } from 'ai';

// Next.js and authentication
import type { NextRequest } from 'next/server';
import type { Session } from 'next-auth';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Utilities
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { generateRichResponse } from '@/lib/utils/chart-data-formatter';

// Types and validation
import type { QueryIntent } from '@/types/analytics';
import { chatbotRequestSchema } from '@/lib/analytics/chatbot/validation';

// Security
import {
  logSecureError,
  getSecureErrorResponse,
  sanitizeDatabaseError,
  validateResponseSecurity,
  detectPromptInjection,
  checkRateLimit
} from '@/lib/analytics/chatbot/security';

// Database operations
import {
  handleMemberDemographicsIntent,
  handleHouseholdInfoIntent,
  handleSacramentRecordsIntent,
  handleCensusParticipationIntent,
  handleTemporalAnalysisIntent,
  handleGeneralIntent
} from '@/lib/analytics/chatbot/database/intent-handlers';
import {
  generateDistributionTable
} from '@/lib/analytics/chatbot/database';

// AI operations
import {
  analyzeUserIntent,
  validateSacramentType,
  buildSystemPromptWithContext,
  determineOptimalTemperature
} from '@/lib/analytics/chatbot/ai';

// Utilities and helpers
import {
  getCachedResult,
  setCachedResult,
  trackError,
  createPerformanceTimer,
  containsSQLCommands,
  sanitizeUserInput,
  sanitizeDatabaseResult,
  validateIntentSecurity
} from '@/lib/analytics/chatbot/utils';

// Configuration
import {
  GEMINI_API_KEY,
  GEMINI_MODEL_NAME,
  REQUEST_TIMEOUT,
  MAX_QUERY_LENGTH,

} from '@/lib/analytics/chatbot/config';



// --- Main API Route Handler ---
// SECURITY NOTICE: This endpoint implements multiple security layers:
// 1. Authentication & Authorization (Admin-only access)
// 2. Rate limiting per user session
// 3. Input sanitization & validation
// 4. SQL injection prevention via Prisma ORM
// 5. Schema information protection
// 6. Personal data access controls
// 7. Error message sanitization
export async function POST(request: NextRequest) {

  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();

  // SECURITY: Create AbortController for proper cleanup and timeout management
  const abortController = new AbortController();
  const timeoutId = setTimeout(() => {
    abortController.abort();
  }, REQUEST_TIMEOUT); // Configurable timeout from config module

  // Declare session variable in outer scope for error handler access
  let session: Session | null = null;

  try {
    // Track request and start performance timer
    const performanceTimer = createPerformanceTimer();
    // Authentication check (copied from original)
    session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      clearTimeout(timeoutId);
      return new Response('Unauthorized access. Admin privileges required.', {
        status: 401,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
      });
    }

    // Rate limiting check
    if (!checkRateLimit(session.user.id)) {
      clearTimeout(timeoutId);
      return new Response(
        'Rate limit exceeded. Too many requests. Please wait before trying again.',
        {
          status: 429,
          headers: { 'Content-Type': 'text/plain; charset=utf-8' },
        }
      );
    }

    // API key check
    if (!GEMINI_API_KEY) {
      clearTimeout(timeoutId);
      if (process.env.NODE_ENV === 'development') {
        console.error('GEMINI_API_KEY not configured');
      }
      return new Response('AI service not configured. Missing API key.', {
        status: 503,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
      });
    }

    // Debug: Log API key status (secure - no key content exposed)
    if (process.env.NODE_ENV === 'development') {
      console.log('GEMINI_API_KEY configured:', !!GEMINI_API_KEY);
    }

    // Request validation
    const body = await request.json();
    const validationResult = chatbotRequestSchema.safeParse(body);

    if (!validationResult.success) {
      clearTimeout(timeoutId);
      const errorMessage = await getSecureErrorResponse(
        'validation',
        request,
        locale
      );
      return new Response(errorMessage, {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
      });
    }

    const { messages } = validationResult.data;

    // Extract the latest user message and conversation history
    const userMessages = messages.filter((m) => m.role === 'user');

    if (userMessages.length === 0) {
      clearTimeout(timeoutId);
      const errorMessage = await getSecureErrorResponse(
        'validation',
        request,
        locale
      );
      return new Response(errorMessage, {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
      });
    }

    // Get the user's latest message to analyze for database queries
    const userMessage = messages.at(-1)?.content || '';

    // SECURITY: Enhanced prompt injection detection and sanitization
    if (detectPromptInjection(userMessage)) {
      clearTimeout(timeoutId);

      // INDUSTRY BEST PRACTICE: Log security violation for monitoring
      if (process.env.NODE_ENV === 'development') {
        console.warn('Prompt injection detected:', userMessage.substring(0, 100));
      }

      // Use centralized alert system for consistent localization
      const errorMessage = await getSecureErrorResponse(
        'validation',
        request,
        locale
      );

      return new Response(errorMessage, {
        status: 400,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'X-Security-Violation': 'prompt-injection-detected', // Signal to client for conversation reset
        },
      });
    }

    // SECURITY: Sanitize user input to prevent injection attacks
    const sanitizedMessage = sanitizeUserInput(userMessage);

    // Enhanced validation after sanitization - allow legitimate short inputs
    if (!sanitizedMessage || sanitizedMessage.trim().length === 0) {
      clearTimeout(timeoutId);
      const errorMessage = await getSecureErrorResponse(
        'validation',
        request,
        locale
      );
      return new Response(errorMessage, {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
      });
    }

    // SECURITY: Additional check for suspicious very short inputs that might be injection attempts
    // Allow legitimate short inputs like greetings ("Hi", "OK", "No", "是") but block suspicious patterns
    if (sanitizedMessage.length < 3) {
      const suspiciousShortPatterns = [
        /^[^a-zA-Z\u4e00-\u9fff\s]+$/, // Only special characters/numbers
        /^\s*[{}[\]|\\]+\s*$/, // Only structural characters
        /^\s*[<>'"]+\s*$/, // Only quote/bracket characters
      ];

      const isSuspiciousShort = suspiciousShortPatterns.some((pattern) =>
        pattern.test(sanitizedMessage)
      );
      if (isSuspiciousShort) {
        clearTimeout(timeoutId);
        const errorMessage = await getSecureErrorResponse(
          'validation',
          request,
          locale
        );
        return new Response(errorMessage, {
          status: 400,
          headers: { 'Content-Type': 'text/plain; charset=utf-8' },
        });
      }
    }

    // Execute hybrid query system (semantic + keyword fallback)
    let databaseContext = '';
    try {
      const queryResults = await executeHybridQuery(
        sanitizedMessage,
        request,
        locale
      );
      if (queryResults.length > 0) {
        // SECURITY: Sanitize database results to prevent context manipulation
        const sanitizedResults = queryResults
          .map((result) => sanitizeDatabaseResult(result))
          .filter((result) => result.length > 0)
          .slice(0, 10); // Limit number of results

        if (sanitizedResults.length > 0) {
          databaseContext = sanitizedResults.join('\n');

          // DEBUG: Log database context in development to verify chart data is preserved
          if (process.env.NODE_ENV === 'development') {
            if (databaseContext.includes('CHART_DATA:')) {
              console.log('Chart data preserved in database context');
            } else {
              console.log('No chart data in database context');
            }
          }
        }
      }
    } catch (queryError) {
      // SECURITY: Secure database error logging
      logSecureError('database_query', queryError, {
        sanitizedMessage: sanitizedMessage.substring(0, 100),
        userId: session?.user?.id,
      });

      // SECURITY: Sanitized database error response
      databaseContext = sanitizeDatabaseError(queryError);
    }

    // Create Google provider with our API key
    const google = createGoogleGenerativeAI({
      apiKey: GEMINI_API_KEY!,
    });

    // 2025 Enhancement: Apply dynamic temperature to main response as well
    const responseTemperature = determineOptimalTemperature(sanitizedMessage);

    if (process.env.NODE_ENV === 'development') {
      console.log('Response temperature:', responseTemperature);
    }

    // Get user's preferred language for AI language instructions
    const userPreferredLanguage =
      locale === 'zh-CN' ? 'Chinese (中文)' : 'English';

    // Use streamText for proper AI SDK streaming with isolated database context
    // INDUSTRY STANDARD 2025: Allow mixed content (text + structured data) for chart generation
    const result = streamText({
      model: google(GEMINI_MODEL_NAME),
      system: buildSystemPromptWithContext(
        databaseContext,
        userPreferredLanguage
      ),
      messages,
      temperature: responseTemperature, // Dynamic temperature for better conversational responses
      maxTokens: 20_480, // Increased for comprehensive analytics responses with charts (2025 optimization)
      // SECURITY: Add AbortController for proper cleanup
      abortSignal: abortController.signal,
      // REMOVED: text-only constraint to enable chart generation
      // Modern AI SDK best practice: Allow structured markers in text responses
    });

    // Track successful response time
    performanceTimer.trackAndGetElapsed();

    // Clear timeout on successful completion
    clearTimeout(timeoutId);

    // Return the streaming response with proper cleanup
    return result.toDataStreamResponse();
  } catch (error) {
    // SECURITY: Ensure proper cleanup on error
    clearTimeout(timeoutId);

    // SECURITY: Secure error logging - detailed logs server-side only
    logSecureError('chatbot_api', error, {
      userId: session?.user?.id,
      requestPath: '/api/admin/analytics/chatbot-ai-sdk',
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });

    // Track error
    trackError();

    // SECURITY: Generic error response with final validation - no sensitive information exposure
    const secureResponse = validateResponseSecurity(
      await getSecureErrorResponse('general', request, locale)
    );
    return new Response(secureResponse, {
      status: 500,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });
  }
}















// Map intent to appropriate database query function
async function executeIntentBasedQuery(
  intent: QueryIntent,
  request: NextRequest,
  locale: 'en' | 'zh-CN',
  userMessage?: string
): Promise<string[]> {
  // Security validation first
  if (!validateIntentSecurity(intent)) {
    return [await getSecureErrorResponse('validation', request, locale)];
  }

  // Confidence validation - handle low confidence (including prompt injection)
  if (intent.confidence <= 0.0) {
    // Zero confidence indicates security issue (prompt injection)
    return [await getSecureErrorResponse('validation', request, locale)];
  }
  if (intent.confidence < 0.1) {
    // Extremely low confidence - likely malformed or suspicious input
    return [await getSecureErrorResponse('validation', request, locale)];
  }
  if (intent.confidence < 0.5) {
    // Low confidence - proceed with general intent but log for monitoring
    // This includes legitimate simple inputs like greetings that may get low confidence
    if (process.env.NODE_ENV === 'development') {
      console.log('Low confidence intent, proceeding with caution:', intent.confidence);
    }
    // Don't block, just proceed with caution - treat as general intent
  }

  const results: string[] = [];

  try {
    // Check if this should return table data for distribution queries
    if (intent.analysisType === 'distribution') {
      const tableData = await generateDistributionTable(intent);
      if (tableData) {
        // Generate rich response with table and charts
        const richResponse = generateRichResponse(
          `Here's the ${tableData.title.toLowerCase()}:`,
          tableData.data,
          tableData.queryType
        );
        results.push(richResponse);
        return results;
      }
    }

    // Route to appropriate handler based on intent for non-distribution queries
    switch (intent.dataType) {
      case 'member_demographics':
        await handleMemberDemographicsIntent(intent, results);
        break;
      case 'household_info':
        await handleHouseholdInfoIntent(intent, results);
        break;
      case 'sacrament_records':
        await handleSacramentRecordsIntent(intent, results);
        break;
      case 'census_participation':
        await handleCensusParticipationIntent(intent, results);
        break;
      case 'temporal_analysis':
        await handleTemporalAnalysisIntent(intent, results);
        break;
      default:
        await handleGeneralIntent(intent, results, userMessage);
        break;
    }

    return results;
  } catch (error) {
    logSecureError('intent_execution', error, {
      intent: intent.dataType,
      analysisType: intent.analysisType,
    });

    return [await getSecureErrorResponse('database', request, locale)];
  }
}

// --- Hybrid Semantic Query System ---

// Main hybrid query executor (Industry-standard approach)
async function executeHybridQuery(
  userMessage: string,
  request: NextRequest,
  locale: 'en' | 'zh-CN'
): Promise<string[]> {
  // Security: Input validation and sanitization
  if (userMessage.length > MAX_QUERY_LENGTH) {
    return ['Query too complex - please simplify your request'];
  }

  // Security: Block SQL injection attempts and raw SQL commands
  if (containsSQLCommands(userMessage)) {
    return [
      'Please ask your question in natural language. SQL commands are not accepted.',
    ];
  }

  // Request deduplication: Check cache first
  const cachedResult = getCachedResult(userMessage);
  if (cachedResult) {
    return cachedResult;
  }

  // INTENT-BASED APPROACH: Use AI-driven intent detection for language consistency
  // This ensures consistent results regardless of input language (English/Chinese)
  if (process.env.NODE_ENV === 'development') {
    console.log('Starting intent-based query analysis');
  }

  // Analyze user intent with AI
  const intent = await analyzeUserIntent(userMessage, request);

  // Validate AI-recognized sacrament type against database
  await validateSacramentType(intent);

  // Execute query based on detected intent
  const result = await executeIntentBasedQuery(
    intent,
    request,
    locale,
    userMessage
  );

  // DEBUGGING: Log intent analysis and results
  if (process.env.NODE_ENV === 'development') {
    console.log('Intent analysis completed, results count:', result.length);
  }

  // Cache the result for future requests
  setCachedResult(userMessage, result);

  return result;
}





// REMOVED: isSimpleQuery function no longer needed



// Note: detectPromptInjection function moved to Intent Analysis System section above



// REMOVED: Semantic layer functions no longer needed
// All queries now use keyword-based approach for consistency

// REMOVED: AI Intent Recognition no longer needed
// All queries now use keyword-based approach for consistency

// REMOVED: Semantic Query Execution no longer needed
// All queries now use keyword-based approach for consistency

// REMOVED: All semantic query functions no longer needed
// All queries now use keyword-based approach for consistency

// REMOVED: All semantic functions no longer needed
// All queries now use keyword-based approach for consistency

// REMOVED: Where clause builders no longer needed
// All queries now use keyword-based approach without filtering

// MOVED: Age-Based Query Handler moved to keyword-queries module

// MOVED: Sacrament Query Handler moved to keyword-queries module

// MOVED: All remaining query handlers moved to keyword-queries module
// - Census Year Query Handler
// - Relationship Query Handler
// - Location/Suburb Query Handler
// - Census Form Status Query Handler