'use client';

import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { useTourContext } from '@/components/tour/TourProvider';
import { scrollToElement } from '@/lib/tour-positioning';
import type {
  ActiveTour,
  TourContent,
  TourState,
  TourTarget,
  TourType,
  UseCensusTourReturn,
} from '@/types/tour';

// Tour storage key
const TOUR_STORAGE_KEY = 'census-tour-state';

/**
 * Custom Census Tour Hook - Professional tour system with smart positioning
 * Replaces Driver.js with custom implementation that prevents clipping issues
 */
export function useCensusTour(): UseCensusTourReturn {
  const [isTourReady, setIsTourReady] = useState(false);
  const [tourState, setTourState] = useState<TourState>({
    isCompleted: false,
    currentStep: 0,
    hasStarted: false,
  });

  const { setActiveTour, setIsVisible } = useTourContext();
  const t = useTranslations('tour');

  // Load tour state from localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(TOUR_STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        setTourState(parsed);
      }
    } catch (error) {
      // Silently handle localStorage errors
    }
  }, []);

  // Save tour state to localStorage
  const saveTourState = useCallback((state: TourState) => {
    try {
      localStorage.setItem(TOUR_STORAGE_KEY, JSON.stringify(state));
      setTourState(state);
    } catch (error) {
      // Silently handle localStorage errors
    }
  }, []);

  // Detect current editing state
  const detectEditingState = useCallback(() => {
    // Check if household head is being edited
    const isEditingHouseholdHead = !!document.querySelector(
      '[data-member-relationship="head"][data-editing="true"]'
    );

    // Check if any member is being edited
    const isEditingAnyMember = !!document.querySelector(
      '[data-editing="true"]'
    );

    // Check if add member form is open
    const isAddMemberFormOpen = !!document.querySelector(
      '[data-tour="add-member-form"]'
    );

    return {
      isEditingHouseholdHead,
      isEditingAnyMember,
      isAddMemberFormOpen,
    };
  }, []);

  // Get context-aware tour targets
  const getContextualTargets = useCallback(
    (tourType: TourType): TourTarget => {
      const editingState = detectEditingState();

      switch (tourType) {
        case 'hobby-fields':
          // Check if edit form is available (user is in edit mode)
          if (document.querySelector('[data-tour="edit-form-hobby"]')) {
            return {
              primary: '[data-tour="edit-form-hobby"]',
              fallback: 'input[name="hobby"]',
              context: 'editing',
            };
          }
          // Otherwise, try to open accordion to get to edit mode
          return {
            primary: '[data-tour="household-head-edit-button"]',
            fallback: '[data-member-relationship="head"]',
            context: 'viewing',
          };

        case 'occupation-fields':
          // Check if edit form is available (user is in edit mode)
          if (document.querySelector('[data-tour="edit-form-occupation"]')) {
            return {
              primary: '[data-tour="edit-form-occupation"]',
              fallback: 'input[name="occupation"]',
              context: 'editing',
            };
          }
          // Otherwise, try to open accordion to get to edit mode
          return {
            primary: '[data-tour="household-head-edit-button"]',
            fallback: '[data-member-relationship="head"]',
            context: 'viewing',
          };

        case 'sacraments':
          // Check if edit form is available (user is in edit mode)
          if (
            document.querySelector('[data-tour="edit-form-sacraments"]') ||
            document.querySelector('[data-tour="add-sacrament-button"]')
          ) {
            // Check if there are existing sacrament items
            const hasSacraments = !!document.querySelector(
              '[data-tour="sacrament-item"]'
            );
            return {
              primary: hasSacraments
                ? '[data-tour="sacrament-item"]'
                : '[data-tour="add-sacrament-button"]',
              fallback: '[data-tour="edit-form-sacraments"]',
              context: hasSacraments
                ? 'editing-has-sacraments'
                : 'editing-no-sacraments',
            };
          }
          // Otherwise, try to open accordion to get to edit mode
          return {
            primary: '[data-tour="household-head-edit-button"]',
            fallback: '[data-member-relationship="head"]',
            context: 'viewing',
          };

        case 'community-feedback':
          // Community feedback always points to the textarea, not edit button
          return {
            primary: '[data-tour="community-feedback-card"] textarea',
            fallback: 'textarea[placeholder*="community"]',
            context: 'feedback-form',
          };

        case 'add-member':
          return {
            primary: '[data-tour="add-member-button"]',
            fallback: 'button[data-tour="add-member"]',
            context: 'viewing',
          };

        case 'household-head-edit':
          if (editingState.isEditingHouseholdHead) {
            return {
              primary: '',
              fallback: '',
              context: 'already-editing',
            };
          }
          return {
            primary: '[data-tour="household-head-edit-button"]',
            fallback: '[data-member-relationship="head"]',
            context: 'viewing',
          };

        default:
          return {
            primary: '[data-tour="add-member-button"]',
            fallback: 'button[data-tour="add-member"]',
            context: 'viewing',
          };
      }
    },
    [detectEditingState]
  );

  // Open household head accordion (critical for tour functionality)
  const openHouseholdHeadAccordion = useCallback(async () => {
    // Find household head member card
    const householdHeadCard = document.querySelector(
      '[data-member-relationship="head"]'
    );
    if (!householdHeadCard) {
      return false;
    }

    // Check if accordion is already open
    const accordionTrigger = householdHeadCard.querySelector(
      '[data-state="open"]'
    );
    if (accordionTrigger) {
      return true;
    }

    // Find and click the accordion trigger
    const trigger = householdHeadCard.querySelector(
      '[data-slot="accordion-trigger"]'
    ) as HTMLElement;
    if (trigger) {
      trigger.click();

      // Wait for accordion to open
      await new Promise((resolve) => setTimeout(resolve, 300));
      return true;
    }

    return false;
  }, []);

  // Wait for edit button to appear after accordion opens
  const waitForEditButton = useCallback(async (maxWaitTime = 2000) => {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const editButton = document.querySelector(
        '[data-tour="household-head-edit-button"]'
      );
      if (editButton) {
        return editButton as HTMLElement;
      }

      // Wait 100ms before checking again
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return null;
  }, []);

  // Find target element with accordion opening logic
  const findTargetElement = useCallback(
    async (targets: TourTarget) => {
      if (!targets.primary) return null;

      let element = document.querySelector(targets.primary) as HTMLElement;

      // If primary target not found and we need to open accordion
      if (
        !element &&
        targets.context === 'viewing' &&
        targets.primary === '[data-tour="household-head-edit-button"]'
      ) {
        const accordionOpened = await openHouseholdHeadAccordion();
        if (accordionOpened) {
          const editButton = await waitForEditButton();
          if (editButton) {
            element = editButton;
          }
        }
      }

      if (!element && targets.fallback) {
        element = document.querySelector(targets.fallback) as HTMLElement;
      }

      return element;
    },
    [openHouseholdHeadAccordion, waitForEditButton]
  );

  // Get tour content based on type and context
  const getTourContent = useCallback(
    (type: TourType, context: string): TourContent => {
      switch (type) {
        case 'hobby-fields':
          return {
            description:
              context === 'editing'
                ? t('hobbyFieldInFormDescription')
                : t('editHouseholdHeadForHobbyDescription'),
          };
        case 'occupation-fields':
          return {
            description:
              context === 'editing'
                ? t('occupationFieldInFormDescription')
                : t('editHouseholdHeadForOccupationDescription'),
          };
        case 'sacraments':
          if (context === 'editing-no-sacraments') {
            return {
              description: t('addSacramentButtonDescription'),
            };
          }
          if (context === 'editing-has-sacraments') {
            return {
              description: t('sacramentsInFormDescription'),
            };
          }
          return {
            description: t('editHouseholdHeadForSacramentsDescription'),
          };
        case 'community-feedback':
          return {
            description: t('communityFeedbackInFormDescription'),
          };
        case 'add-member':
          return {
            description: t('addMemberDescription'),
          };
        case 'household-head-edit':
          return {
            description: t('editHouseholdHeadDescription'),
          };
        default:
          return {
            description: t('addMemberDescription'),
          };
      }
    },
    [t]
  );

  // Start the tour with custom implementation
  const startTour = useCallback(
    async (tourType: TourType = 'add-member') => {
      if (!isTourReady) {
        return;
      }

      // Clear any existing tour first to prevent conflicts
      setActiveTour(null);
      setIsVisible(false);

      // Create cancellation token to prevent race conditions
      let isCancelled = false;

      try {
        const targets = getContextualTargets(tourType);

        // Handle special case where user is already in the desired state
        if (targets.context === 'already-editing') {
          return;
        }

        const element = await findTargetElement(targets);

        // Check if operation was cancelled during async operation
        if (isCancelled) {
          return;
        }

        if (!element) {
          return;
        }

        const content = getTourContent(tourType, targets.context);

        // Scroll to element with header offset
        scrollToElement(element, 100);

        // Create active tour object
        const activeTour: ActiveTour = {
          type: tourType,
          element,
          selector: targets.primary,
          content,
          context: targets.context,
        };

        // Set up interaction handlers to dismiss tour when user actually interacts
        let autoHideTimeout: NodeJS.Timeout | null = null;
        let cleanupFunctions: (() => void)[] = [];

        const interactionHandler = () => {
          // Clear auto-hide timeout
          if (autoHideTimeout) {
            clearTimeout(autoHideTimeout);
            autoHideTimeout = null;
          }

          // Clean up all event listeners
          cleanupFunctions.forEach((cleanup) => cleanup());
          cleanupFunctions = [];

          setActiveTour(null);
          setIsVisible(false);

          // Save completion state
          saveTourState({
            isCompleted: true,
            currentStep: 1,
            hasStarted: true,
          });
        };

        // Listen for actual interaction events (delayed to allow interaction to complete first)
        const delayedInteractionHandler = () => {
          setTimeout(interactionHandler, 100);
        };

        // Add event listeners and store cleanup functions
        if (element) {
          element.addEventListener('click', delayedInteractionHandler);
          cleanupFunctions.push(() =>
            element.removeEventListener('click', delayedInteractionHandler)
          );

          element.addEventListener('input', delayedInteractionHandler);
          cleanupFunctions.push(() =>
            element.removeEventListener('input', delayedInteractionHandler)
          );

          element.addEventListener('change', delayedInteractionHandler);
          cleanupFunctions.push(() =>
            element.removeEventListener('change', delayedInteractionHandler)
          );

          element.addEventListener('focus', delayedInteractionHandler);
          cleanupFunctions.push(() =>
            element.removeEventListener('focus', delayedInteractionHandler)
          );
        }

        // Start the tour
        setActiveTour(activeTour);
        setIsVisible(true);

        // Auto-dismiss tour after 30 seconds if no interaction
        autoHideTimeout = setTimeout(() => {
          // Cancel any ongoing operations
          isCancelled = true;
          // Clean up event listeners before dismissing
          cleanupFunctions.forEach((cleanup) => cleanup());
          cleanupFunctions = [];
          setActiveTour(null);
          setIsVisible(false);
        }, 30_000);

        // Save tour state
        saveTourState({
          isCompleted: false,
          currentStep: 0,
          hasStarted: true,
        });
      } catch (error) {
        // Silent error handling for production
      }
    },
    [
      isTourReady,
      getContextualTargets,
      findTargetElement,
      getTourContent,
      setActiveTour,
      setIsVisible,
      saveTourState,
    ]
  );

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clean up any active tour when component unmounts
      setActiveTour(null);
      setIsVisible(false);
    };
  }, [setActiveTour, setIsVisible]);

  // Reset tour state
  const resetTour = useCallback(() => {
    try {
      localStorage.removeItem(TOUR_STORAGE_KEY);
      setTourState({
        isCompleted: false,
        currentStep: 0,
        hasStarted: false,
      });
    } catch (error) {
      // Silently handle localStorage errors
    }
  }, []);

  // Initialize tour system
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTourReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return {
    startTour,
    resetTour,
    tourState,
    isTourReady,
  };
}
