'use client';

import { Languages } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface LanguageSelectorProps {
  variant?: 'default' | 'sidebar' | 'header';
}

// Define supported languages for next-intl
const languages = [
  { code: 'en', label: 'English', nativeLabel: 'English' },
  { code: 'zh-CN', label: '中文', nativeLabel: '中文' },
] as const;

export function LanguageSelector({
  variant = 'default',
}: LanguageSelectorProps) {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations('common');

  const handleLanguageChange = (newLocale: string) => {
    // Set the locale cookie for next-intl
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}; SameSite=Lax`;

    // Refresh the page to apply the new locale
    router.refresh();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="cursor-pointer">
        <Button
          aria-label={t('changeLanguage')}
          className={
            variant === 'sidebar'
              ? 'rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
              : variant === 'header'
                ? 'h-8 w-8 rounded-full hover:bg-muted'
                : 'rounded-full'
          }
          size="icon"
          style={{ cursor: 'pointer' }}
          variant="ghost"
        >
          <Languages className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languages.map((language) => (
          <DropdownMenuItem
            className="cursor-pointer"
            data-active={locale === language.code}
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
          >
            {language.nativeLabel}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
