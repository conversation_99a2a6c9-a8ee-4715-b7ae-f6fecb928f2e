/**
 * Census Controls Settings Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import { prisma } from './prisma';

/**
 * Get census controls settings
 */
export async function getCensusControlsSettings(): Promise<{
  systemOpen: boolean;
  autoOpenClose: boolean;
  censusStartDate: string | null;
  censusEndDate: string | null;
  manualOverride: boolean;
}> {
  const settings = await prisma.systemSettings.findMany({
    where: {
      settingKey: {
        in: [
          'system_open',
          'auto_open_close',
          'census_start_date',
          'census_end_date',
          'manual_override',
        ],
      },
    },
  });

  // Create a map of settings
  const settingsMap: Record<string, string> = {};
  settings.forEach((setting) => {
    settingsMap[setting.settingKey] = setting.settingValue || '';
  });

  return {
    systemOpen: settingsMap['system_open'] === 'true',
    autoOpenClose: settingsMap['auto_open_close'] === 'true',
    censusStartDate: settingsMap['census_start_date'] || null,
    censusEndDate: settingsMap['census_end_date'] || null,
    manualOverride: settingsMap['manual_override'] === 'true',
  };
}

/**
 * Update census controls settings
 */
export async function updateCensusControlsSettings(settings: {
  systemOpen: boolean;
  autoOpenClose: boolean;
  censusStartDate: string | null;
  censusEndDate: string | null;
  manualOverride?: boolean;
}): Promise<void> {
  // Create an array of upsert operations for the transaction
  const updates = [
    prisma.systemSettings.upsert({
      where: { settingKey: 'system_open' },
      update: { settingValue: settings.systemOpen.toString() },
      create: {
        settingKey: 'system_open',
        settingValue: settings.systemOpen.toString(),
      },
    }),
    prisma.systemSettings.upsert({
      where: { settingKey: 'auto_open_close' },
      update: { settingValue: settings.autoOpenClose.toString() },
      create: {
        settingKey: 'auto_open_close',
        settingValue: settings.autoOpenClose.toString(),
      },
    }),
  ];

  // Add manual override if provided
  if (settings.manualOverride !== undefined) {
    updates.push(
      prisma.systemSettings.upsert({
        where: { settingKey: 'manual_override' },
        update: { settingValue: settings.manualOverride.toString() },
        create: {
          settingKey: 'manual_override',
          settingValue: settings.manualOverride.toString(),
        },
      })
    );
  }

  // Add start date if provided
  if (settings.censusStartDate !== null) {
    updates.push(
      prisma.systemSettings.upsert({
        where: { settingKey: 'census_start_date' },
        update: { settingValue: settings.censusStartDate },
        create: {
          settingKey: 'census_start_date',
          settingValue: settings.censusStartDate,
        },
      })
    );
  }

  // Add end date if provided
  if (settings.censusEndDate !== null) {
    updates.push(
      prisma.systemSettings.upsert({
        where: { settingKey: 'census_end_date' },
        update: { settingValue: settings.censusEndDate },
        create: {
          settingKey: 'census_end_date',
          settingValue: settings.censusEndDate,
        },
      })
    );
  }

  // Execute all updates in a single transaction
  await prisma.$transaction(updates);
}
