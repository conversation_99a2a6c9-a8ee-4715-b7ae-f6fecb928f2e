/**
 * Household Member Database Operations with Prisma - Consolidated Professional Implementation
 *
 * This file consolidates household-members.ts and household-members-prisma.ts into a single source of truth.
 * Features:
 * - Consistent data structures and relationship management
 * - Professional Prisma ORM implementation
 * - Comprehensive household member operations
 * - Type-safe operations throughout
 * - Proper data transformation utilities
 */

import type {
  CensusYear,
  Household,
  HouseholdMember,
  Member,
} from '@prisma/client';
import { calculateAge } from '../utils/date-calculations';
import { prisma } from './prisma';

// Type definitions for complex household member data
export interface IHouseholdMemberWithRelations extends HouseholdMember {
  member?: Member;
  household?: Household;
  censusYear?: CensusYear;
}

export interface IHouseholdMemberWithDetails extends HouseholdMember {
  memberId: number; // Frontend compatibility mapping from memberId
  firstName: string;
  lastName: string;
  dateOfBirth: Date | null;
  gender: 'male' | 'female' | 'other';
  mobilePhone: string;
  hobby?: string | null;
  occupation?: string | null;
  age?: number | null;
}

/**
 * Get all household members with optional filtering
 */
export async function getHouseholdMembers(
  options: {
    limit?: number;
    offset?: number;
    householdId?: number;
    censusYearId?: number;
    includeRelations?: boolean;
    currentOnly?: boolean;
  } = {}
): Promise<IHouseholdMemberWithRelations[]> {
  const {
    limit = 100,
    offset = 0,
    householdId,
    censusYearId,
    includeRelations = false,
    currentOnly = true,
  } = options;

  // Build where clause
  const where: Record<string, unknown> = {};

  if (householdId) {
    where.householdId = householdId;
  }

  if (censusYearId) {
    where.censusYearId = censusYearId;
  } else if (currentOnly) {
    where.isCurrent = true;
  }

  // Build include object
  const include = includeRelations
    ? {
        member: true,
        household: true,
        censusYear: true,
      }
    : undefined;

  // Execute query with Prisma
  const householdMembers = await prisma.householdMember.findMany({
    where,
    include,
    take: limit,
    skip: offset,
    orderBy: [{ householdId: 'asc' }, { relationship: 'asc' }],
  });

  return householdMembers;
}

/**
 * Get household member by ID
 */
export async function getHouseholdMemberById(
  id: number,
  includeRelations = false
): Promise<IHouseholdMemberWithRelations | null> {
  const include = includeRelations
    ? {
        member: true,
        household: true,
        censusYear: true,
      }
    : undefined;

  return await prisma.householdMember.findUnique({
    where: { id },
    include,
  });
}

/**
 * Create a new household member relationship
 */
export async function createHouseholdMember(data: {
  householdId: number;
  memberId: number;
  relationship: 'head' | 'spouse' | 'child' | 'parent' | 'relative' | 'other';
  censusYearId: number;
  isCurrent?: boolean;
}): Promise<HouseholdMember> {
  return await prisma.householdMember.create({
    data: {
      householdId: data.householdId,
      memberId: data.memberId,
      relationship: data.relationship,
      censusYearId: data.censusYearId,
      isCurrent: data.isCurrent ?? true,
    },
  });
}

/**
 * Update an existing household member relationship
 */
export async function updateHouseholdMember(
  id: number,
  data: Partial<{
    relationship: 'head' | 'spouse' | 'child' | 'parent' | 'relative' | 'other';
    censusYearId: number;
    isCurrent: boolean;
  }>
): Promise<HouseholdMember | null> {
  try {
    return await prisma.householdMember.update({
      where: { id },
      data,
    });
  } catch (error) {
    console.error('Error updating household member:', error);
    return null;
  }
}

/**
 * Delete a household member relationship
 */
export async function deleteHouseholdMember(id: number): Promise<boolean> {
  try {
    await prisma.householdMember.delete({
      where: { id },
    });
    return true;
  } catch (error) {
    console.error('Error deleting household member:', error);
    return false;
  }
}

/**
 * Get household members by household ID
 */
export async function getHouseholdMembersByHouseholdId(
  householdId: number,
  censusYearId?: number,
  currentOnly = true
): Promise<IHouseholdMemberWithRelations[]> {
  const where: Record<string, unknown> = { householdId };

  if (censusYearId) {
    where.censusYearId = censusYearId;
  } else if (currentOnly) {
    where.isCurrent = true;
  }

  return await prisma.householdMember.findMany({
    where,
    include: {
      member: true,
      censusYear: true,
    },
    orderBy: [{ relationship: 'asc' }, { member: { firstName: 'asc' } }],
  });
}

/**
 * Get household members by member ID
 */
export async function getHouseholdMembersByMemberId(
  memberId: number,
  currentOnly = true
): Promise<IHouseholdMemberWithRelations[]> {
  const where: Record<string, unknown> = { memberId };

  if (currentOnly) {
    where.isCurrent = true;
  }

  return await prisma.householdMember.findMany({
    where,
    include: {
      household: true,
      censusYear: true,
    },
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Set all household members as inactive except specified ones
 */
export async function setAllHouseholdMembersInactive(
  householdId: number,
  exceptMemberIds: number[] = []
): Promise<void> {
  const where: Record<string, unknown> = {
    householdId,
    isCurrent: true,
  };

  if (exceptMemberIds.length > 0) {
    where.memberId = {
      notIn: exceptMemberIds,
    };
  }

  await prisma.householdMember.updateMany({
    where,
    data: {
      isCurrent: false,
    },
  });
}

/**
 * Get household members with detailed member information
 */
export async function getHouseholdMemberWithDetails(
  householdId: number,
  censusYearId?: number,
  includeInactive = false
): Promise<IHouseholdMemberWithDetails[]> {
  const where: Record<string, unknown> = { householdId };

  if (censusYearId) {
    where.censusYearId = censusYearId;
  } else if (!includeInactive) {
    where.isCurrent = true;
  }
  // If includeInactive is true and no censusYearId, we get ALL members regardless of isCurrent

  const householdMembers = await prisma.householdMember.findMany({
    where,
    include: {
      member: true,
    },
    orderBy: [{ relationship: 'asc' }, { member: { firstName: 'asc' } }],
  });

  // Transform to include member details at the top level and calculate age
  return householdMembers.map((hm) => ({
    ...hm,
    memberId: hm.memberId, // Map memberId to memberId for frontend compatibility
    firstName: hm.member!.firstName,
    lastName: hm.member!.lastName,
    dateOfBirth: hm.member!.dateOfBirth,
    gender: hm.member!.gender,
    mobilePhone: hm.member!.mobilePhone,
    hobby: hm.member!.hobby,
    occupation: hm.member!.occupation,
    age: calculateAge(hm.member!.dateOfBirth),
  }));
}

/**
 * Check if a member is already in a household for a specific census year
 */
export async function isMemberInHousehold(
  memberId: number,
  householdId: number,
  censusYearId: number
): Promise<boolean> {
  const existing = await prisma.householdMember.findFirst({
    where: {
      memberId,
      householdId,
      censusYearId,
    },
  });

  return !!existing;
}

/**
 * Get household head for a specific household
 */
export async function getHouseholdHead(
  householdId: number,
  censusYearId?: number
): Promise<IHouseholdMemberWithRelations | null> {
  const where: Record<string, unknown> = {
    householdId,
    relationship: 'head',
  };

  if (censusYearId) {
    where.censusYearId = censusYearId;
  } else {
    where.isCurrent = true;
  }

  return await prisma.householdMember.findFirst({
    where,
    include: {
      member: true,
      household: true,
      censusYear: true,
    },
  });
}

/**
 * Update household member relationship
 */
export async function updateHouseholdMemberRelationship(
  householdId: number,
  memberId: number,
  censusYearId: number,
  relationship: 'head' | 'spouse' | 'child' | 'parent' | 'relative' | 'other'
): Promise<HouseholdMember | null> {
  try {
    return await prisma.householdMember.update({
      where: {
        householdId_memberId_censusYearId: {
          householdId,
          memberId,
          censusYearId,
        },
      },
      data: { relationship },
    });
  } catch (error) {
    console.error('Error updating household member relationship:', error);
    return null;
  }
}

/**
 * Remove member from household
 */
export async function removeMemberFromHousehold(
  householdId: number,
  memberId: number,
  censusYearId: number
): Promise<boolean> {
  try {
    await prisma.householdMember.delete({
      where: {
        householdId_memberId_censusYearId: {
          householdId,
          memberId,
          censusYearId,
        },
      },
    });
    return true;
  } catch (error) {
    console.error('Error removing member from household:', error);
    return false;
  }
}

/**
 * Get household member statistics
 */
export async function getHouseholdMemberStatistics(
  censusYearId?: number
): Promise<{
  totalMembers: number;
  relationshipDistribution: Record<string, number>;
  householdsWithMembers: number;
}> {
  const where = censusYearId ? { censusYearId } : { isCurrent: true };

  // Get total members
  const totalMembers = await prisma.householdMember.count({ where });

  // Get relationship distribution
  const relationshipStats = await prisma.householdMember.groupBy({
    by: ['relationship'],
    where,
    _count: {
      relationship: true,
    },
  });

  const relationshipDistribution = relationshipStats.reduce(
    (acc, stat) => {
      acc[stat.relationship] = stat._count.relationship;
      return acc;
    },
    {} as Record<string, number>
  );

  // Get households with members
  const householdsWithMembers = await prisma.householdMember.findMany({
    where,
    select: { householdId: true },
    distinct: ['householdId'],
  });

  return {
    totalMembers,
    relationshipDistribution,
    householdsWithMembers: householdsWithMembers.length,
  };
}
