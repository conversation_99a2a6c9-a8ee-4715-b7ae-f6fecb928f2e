/**
 * Utility functions for formatting database query results into chart data
 * and generating rich markdown responses with embedded charts
 *
 * 2025 Performance Optimizations:
 * - Memory-efficient data processing
 * - BigInt handling for PostgreSQL
 * - Sample-based analysis for large datasets
 * - Data reuse to prevent duplication
 */

export interface ChartRecommendation {
  type: string;
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
  alternatives?: string[];
}

export interface ChartData {
  type:
    | 'bar'
    | 'pie'
    | 'line'
    | 'area'
    | 'scatter'
    | 'heatmap'
    | 'radar'
    | 'funnel'
    | 'treemap'
    | 'sankey'
    | 'waffle'
    | 'table';
  title?: string;
  data: Array<Record<string, unknown>>;
  xKey?: string;
  yKey?: string;
  nameKey?: string;
  valueKey?: string;
  // Chart recommendation metadata
  recommendation?: {
    requestedType?: string;
    suggestedType: string;
    confidence: number;
    reasoning: string;
    alternatives?: Array<{
      type: string;
      suitability: number;
      reason: string;
    }>;
    userOverride?: boolean;
  };
  // Extended properties for advanced charts
  config?: {
    // Scatter plot specific
    sizeKey?: string;
    colorKey?: string;
    // Heatmap specific
    xAxisKey?: string;
    yAxisKey?: string;
    valueKey?: string;
    // Radar chart specific
    metrics?: string[];
    // Area chart specific
    stackedKeys?: string[];
    // Treemap specific
    hierarchyKeys?: string[];
    // Sankey specific
    sourceKey?: string;
    targetKey?: string;
    // General styling
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
    responsive?: boolean;
  };
}

/**
 * Simple fallback chart type when no LLM recommendation is available
 * 2025 Performance: Optimized with early returns and sample-based detection
 */
function getDefaultChartType(data: Array<Record<string, unknown>>): string {
  if (!data || data.length === 0) return 'table';

  const firstRow = data[0];
  if (!firstRow) return 'table';

  const keys = Object.keys(firstRow);
  if (keys.length === 0) return 'table';

  // 2025 Optimization: Use sample for type detection to avoid processing all data
  const sampleSize = Math.min(10, data.length);
  const sampleData = data.slice(0, sampleSize);

  const numericKeys = keys.filter((key) => {
    // Early return for non-statistical columns
    if (isNonStatisticalColumn(key)) return false;

    // Check if all sample values are numeric
    return sampleData.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        typeof v === 'bigint' ||
        (typeof v === 'string' && v.trim() !== '' && !isNaN(Number(v)))
      );
    });
  });

  // Simple fallback: bar chart for most cases, table if no numeric data
  return numericKeys.length > 0 ? 'bar' : 'table';
}

/**
 * Analyze query results and determine the best chart type using LLM recommendation
 * 2025 Performance: Enhanced validation and error handling
 */
export function analyzeDataForChart(
  data: Array<Record<string, unknown>>,
  chartRecommendation?: ChartRecommendation
): ChartData | null {
  // 2025 Validation: Enhanced input validation
  if (!data || !Array.isArray(data) || data.length === 0) return null;

  // 2025 Performance Optimization: Limit analysis for very large datasets
  const ANALYSIS_SAMPLE_SIZE = 100;
  const sampleData = data.length > ANALYSIS_SAMPLE_SIZE
    ? data.slice(0, ANALYSIS_SAMPLE_SIZE)
    : data;

  const firstRow = data[0];
  const keys = Object.keys(firstRow);

  // 2025 Memory Optimization: Use sample for type analysis, full data for chart
  const numericKeys = keys.filter((key) => {
    // Early return for non-statistical columns
    if (isNonStatisticalColumn(key)) return false;

    // Check if all sample values are numeric (including BigInt from PostgreSQL)
    return sampleData.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        typeof v === 'bigint' ||
        (typeof v === 'string' && v.trim() !== '' && !isNaN(Number(v)))
      );
    });
  });

  const categoricalKeys = keys.filter(
    (key) => !(numericKeys.includes(key) || isNonStatisticalColumn(key))
  );

  // Use LLM recommendation or fallback to simple detection
  const chartType = chartRecommendation?.type || getDefaultChartType(data);

  // Create recommendation metadata from LLM response
  const recommendation = chartRecommendation
    ? {
        suggestedType: chartRecommendation.type,
        confidence:
          chartRecommendation.confidence === 'high'
            ? 90
            : chartRecommendation.confidence === 'medium'
              ? 70
              : 50,
        reasoning:
          chartRecommendation.reasoning || 'LLM recommended chart type',
        alternatives: (chartRecommendation.alternatives || []).map((alt) => ({
          type: alt,
          suitability: 70, // Default suitability for alternatives
          reason: 'Alternative chart type suggested by AI',
        })),
      }
    : undefined;

  // Generate chart data based on chart type (simplified)
  let chartData: ChartData | null = null;

  // Handle different chart types with simplified logic
  if (!chartData && chartType === 'scatter' && numericKeys.length >= 2) {
    const [xKey, yKey] = numericKeys;
    const categoryKey = categoricalKeys[0];

    // 2025 Performance: Single pass data transformation with proper BigInt handling
    chartData = {
      type: 'scatter',
      data: data.map((row, index) => ({
        x: safeToNumber(row[xKey]),
        y: safeToNumber(row[yKey]),
        name: categoryKey ? String(row[categoryKey] || `Point ${index + 1}`) : `Point ${index + 1}`,
      })),
      xKey: 'x',
      yKey: 'y',
      recommendation,
      config: { responsive: true },
    };
  }

  // 2. Heatmap: 3 columns (x, y, value) or matrix-like data
  if (
    !chartData &&
    keys.length === 3 &&
    numericKeys.length >= 1 &&
    categoricalKeys.length >= 2
  ) {
    const valueKey = numericKeys[0];
    const [xKey, yKey] = categoricalKeys;

    // 2025 Performance: Optimized data transformation with BigInt handling
    chartData = {
      type: 'heatmap',
      data: data.map((row) => ({
        x: String(row[xKey] || ''),
        y: String(row[yKey] || ''),
        value: safeToNumber(row[valueKey]),
      })),
      valueKey: 'value',
      recommendation,
      config: {
        xAxisKey: 'x',
        yAxisKey: 'y',
        valueKey: 'value',
        responsive: true,
        showLegend: true,
      },
    };
  }

  // 3. Area chart: Time series with multiple metrics
  if (
    !chartData &&
    keys.some(
      (key) =>
        key.toLowerCase().includes('date') ||
        key.toLowerCase().includes('time') ||
        key.toLowerCase().includes('year')
    )
  ) {
    const dateKey = keys.find(
      (key) =>
        key.toLowerCase().includes('date') ||
        key.toLowerCase().includes('time') ||
        key.toLowerCase().includes('year')
    );

    if (dateKey && numericKeys.length >= 2) {
      // 2025 Performance: Optimized area chart data with BigInt handling
      chartData = {
        type: 'area',
        data: data.map((row) => {
          const result: Record<string, unknown> = {
            date: String(row[dateKey] || ''),
          };

          // Efficiently process numeric keys with BigInt support
          for (const key of numericKeys) {
            result[key] = safeToNumber(row[key]);
          }

          return result;
        }),
        xKey: 'date',
        recommendation,
        config: {
          stackedKeys: numericKeys,
          showGrid: true,
          responsive: true,
        },
      };
    }
  }

  // 4. Radar chart: Multiple metrics for comparison
  if (!chartData && numericKeys.length >= 3 && data.length <= 10) {
    const categoryKey = categoricalKeys[0];

    // 2025 Performance: Optimized radar chart data processing
    chartData = {
      type: 'radar',
      data: data.map((row, index) => {
        const result: Record<string, unknown> = {
          name: categoryKey ? String(row[categoryKey] || `Item ${index + 1}`) : `Item ${index + 1}`,
        };

        // Efficiently process numeric keys with BigInt support
        for (const key of numericKeys) {
          result[key] = safeToNumber(row[key]);
        }

        return result;
      }),
      recommendation,
      config: {
        metrics: numericKeys,
        responsive: true,
        showLegend: true,
      },
    };
  }

  // 5. Treemap: Hierarchical data with size values
  if (!chartData && categoricalKeys.length >= 2 && numericKeys.length >= 1) {
    const valueKey = numericKeys[0];
    const [nameKey, categoryKey] = categoricalKeys;

    // 2025 Performance: Optimized treemap data with BigInt handling
    chartData = {
      type: 'treemap',
      data: data.map((row) => ({
        name: String(row[nameKey] || 'Unknown'),
        category: String(row[categoryKey] || 'default'),
        value: safeToNumber(row[valueKey]),
      })),
      valueKey: 'value',
      recommendation,
      config: {
        hierarchyKeys: categoricalKeys,
        valueKey: 'value',
        responsive: true,
      },
    };
  }

  // Traditional charts (most common case)
  if (!chartData && keys.length === 2) {
    const [nameKey, valueKey] = keys;
    const firstValue = firstRow[valueKey];

    // 2025 Performance: Improved numeric detection with BigInt support
    if (
      typeof firstValue === 'number' ||
      typeof firstValue === 'bigint' ||
      (typeof firstValue === 'string' &&
        firstValue.trim() !== '' &&
        !isNaN(Number(firstValue)))
    ) {
      // Single pass data transformation with proper BigInt handling
      chartData = {
        type: chartType as 'bar' | 'pie' | 'waffle',
        data: data.map((row) => ({
          name: String(row[nameKey] || 'Unknown'),
          value: safeToNumber(row[valueKey]),
        })),
        nameKey: 'name',
        valueKey: 'value',
        xKey: 'name',
        yKey: 'value',
        recommendation,
        config: {
          responsive: true,
          showLegend: ['pie', 'waffle'].includes(chartType),
        },
      };
    }
  }

  // Fallback: If no chart data generated yet, create a simple chart
  if (!chartData && numericKeys.length > 0) {
    const nameKey = categoricalKeys[0] || keys[0];
    const valueKey = numericKeys[0];

    // 2025 Performance: Optimized fallback chart with BigInt handling
    chartData = {
      type: chartType as 'bar' | 'pie' | 'waffle' | 'line',
      data: data.map((row) => ({
        name: String(row[nameKey] || 'Unknown'),
        value: safeToNumber(row[valueKey]),
      })),
      nameKey: 'name',
      valueKey: 'value',
      xKey: 'name',
      yKey: 'value',
      recommendation,
      config: {
        responsive: true,
        showLegend: ['pie', 'waffle'].includes(chartType),
      },
    };
  }

  return chartData;
}

/**
 * Format a cell value based on its column type
 */
function formatCellValue(value: unknown, columnName: string): string {
  if (value === null || value === undefined) return '';

  const stringValue = String(value);

  // Handle phone numbers - keep as string, don't format as number
  if (isNonStatisticalColumn(columnName)) {
    return stringValue;
  }

  // Handle regular numeric values
  if (
    typeof value === 'number' ||
    (!isNaN(Number(stringValue)) && stringValue.trim() !== '')
  ) {
    const numValue = Number(stringValue);
    // Only format as number if it's not a phone/ID field and is a reasonable number
    if (!isNonStatisticalColumn(columnName) && numValue < 1_000_000_000) {
      return numValue.toLocaleString();
    }
  }

  return stringValue;
}

/**
 * Format query results into a markdown table
 * 2025 Performance: Optimized string operations and memory usage
 */
export function formatAsMarkdownTable(
  data: Array<Record<string, unknown>>
): string {
  if (!data || data.length === 0) return 'No data available.';

  const firstRow = data[0];
  if (!firstRow) return 'No data available.';

  const keys = Object.keys(firstRow);
  if (keys.length === 0) return 'No data available.';

  // 2025 Optimization: Pre-allocate array with known size
  const result: string[] = new Array(data.length + 2);

  // Create header and separator
  result[0] = `| ${keys.join(' | ')} |`;
  result[1] = `| ${keys.map(() => '---').join(' | ')} |`;

  // 2025 Performance: Single pass row processing with efficient string operations
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const cells: string[] = new Array(keys.length);

    for (let j = 0; j < keys.length; j++) {
      const key = keys[j];
      const cellValue = formatCellValue(row[key], key);
      // Efficient escape operations
      cells[j] = cellValue
        .replaceAll('|', '\\|')
        .replaceAll('\n', ' ')
        .replaceAll('\r', ' ')
        .replaceAll('`', '\\`');
    }

    result[i + 2] = `| ${cells.join(' | ')} |`;
  }

  return result.join('\n');
}

/**
 * Generate rich markdown response with embedded charts and tables
 */
export function generateRichResponse(
  aiResponse: string,
  queryResult: Array<Record<string, unknown>> | null,
  queryType?: string,
  chartRecommendation?: ChartRecommendation
): string {
  if (!queryResult || queryResult.length === 0) {
    return aiResponse;
  }

  let richResponse = aiResponse;

  // Analyze data for potential charts using LLM recommendation
  const chartData = analyzeDataForChart(queryResult, chartRecommendation);

  if (chartData && queryResult.length > 1) {
    // Add chart
    const chartTitle = generateChartTitle(queryResult, queryType);
    chartData.title = chartTitle;

    richResponse += '\n\n## 📊 Data Visualization\n\n';
    richResponse += `CHART_DATA:${JSON.stringify(chartData)}\n\n`;
  }

  // Add formatted table with export data
  richResponse += '\n\n## 📋 Detailed Results\n\n';

  // 2025 Memory Optimization: Reuse chart data if available to avoid duplication
  let serializedData: Array<Record<string, unknown>>;

  if (chartData && chartData.data && Array.isArray(chartData.data)) {
    // Reuse chart data to avoid memory duplication
    serializedData = chartData.data as Array<Record<string, unknown>>;
  } else {
    // 2025 Performance: Optimized BigInt conversion with single pass
    serializedData = new Array(queryResult.length);

    for (let i = 0; i < queryResult.length; i++) {
      const row = queryResult[i];
      const serializedRow: Record<string, unknown> = {};

      // Efficient BigInt conversion
      for (const [key, value] of Object.entries(row)) {
        serializedRow[key] = typeof value === 'bigint' ? Number(value) : value;
      }

      serializedData[i] = serializedRow;
    }
  }

  const exportMetadata = {
    data: serializedData,
    title: generateChartTitle(serializedData, queryType),
    queryType: queryType || 'general',
    totalRecords: serializedData.length,
  };
  richResponse += `TABLE_EXPORT_DATA:${JSON.stringify(exportMetadata)}\n\n`;

  if (queryResult.length <= 20) {
    // For small datasets, show full table
    richResponse += formatAsMarkdownTable(queryResult);
  } else {
    // For large datasets, show summary + sample
    richResponse += `**Total Records:** ${queryResult.length}\n\n`;
    richResponse += '**Sample Data (first 10 records):**\n\n';
    richResponse += formatAsMarkdownTable(queryResult.slice(0, 10));
    richResponse += `\n\n*... and ${queryResult.length - 10} more records*`;
  }

  // Add SQL query if available (this would be passed from the API)
  // richResponse += `\n\n## 🔍 Query Details\n\n`;
  // richResponse += '```sql\n' + sqlQuery + '\n```';

  return richResponse;
}

/**
 * Generate appropriate chart title based on data and query type
 */
function generateChartTitle(
  data: Array<Record<string, unknown>>,
  queryType?: string
): string {
  const keys = Object.keys(data[0]);

  // Try to infer from column names
  if (keys.some((key) => key.toLowerCase().includes('suburb'))) {
    return 'Distribution by Suburb';
  }

  if (
    keys.some(
      (key) =>
        key.toLowerCase().includes('gender') ||
        key.toLowerCase().includes('sex')
    )
  ) {
    return 'Gender Distribution';
  }

  if (keys.some((key) => key.toLowerCase().includes('age'))) {
    return 'Age Distribution';
  }

  if (keys.some((key) => key.toLowerCase().includes('year'))) {
    return 'Yearly Trends';
  }

  if (keys.some((key) => key.toLowerCase().includes('month'))) {
    return 'Monthly Distribution';
  }

  // Fallback based on query type
  switch (queryType) {
    case 'members':
      return 'Member Statistics';
    case 'households':
      return 'Household Statistics';
    case 'unique_codes':
      return 'Unique Code Statistics';
    default:
      return 'Data Distribution';
  }
}

/**
 * Detect if query results contain numerical data suitable for charts
 */
export function hasNumericalData(
  data: Array<Record<string, unknown>>
): boolean {
  if (!data || data.length === 0) return false;

  const firstRow = data[0];
  return Object.values(firstRow).some((value) => {
    return (
      typeof value === 'number' ||
      (typeof value === 'string' &&
        value.trim() !== '' &&
        !isNaN(Number(value)))
    );
  });
}

/**
 * Format large numbers for display
 */
export function formatNumber(num: number): string {
  if (num >= 1_000_000) {
    return (num / 1_000_000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * Safe conversion of values to numbers with BigInt support
 * 2025 Performance: Optimized type conversion
 */
function safeToNumber(value: unknown): number {
  if (typeof value === 'number') return value;
  if (typeof value === 'bigint') return Number(value);
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '') return 0;
    const parsed = Number(trimmed);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

/**
 * Check if a column should be excluded from statistical analysis
 */
function isNonStatisticalColumn(columnName: string): boolean {
  const excludePatterns = [
    /phone/i,
    /mobile/i,
    /contact/i,
    /id$/i,
    /_id$/i,
    /code/i,
    /postal/i,
    /zip/i,
    /ssn/i,
    /abn/i,
    /acn/i,
    /number$/i,
    /num$/i,
  ];

  return excludePatterns.some((pattern) => pattern.test(columnName));
}

/**
 * Generate summary statistics for numerical columns (excluding IDs, phone numbers, etc.)
 */
export function generateSummaryStats(
  data: Array<Record<string, unknown>>
): string {
  if (!data || data.length === 0) return '';

  const numericalColumns = Object.keys(data[0]).filter((key) => {
    // Check if column is numeric with improved detection
    const isNumeric = data.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        (typeof v === 'string' && v.trim() !== '' && !isNaN(Number(v)))
      );
    });

    // Exclude non-statistical columns (phone numbers, IDs, etc.)
    const isStatistical = !isNonStatisticalColumn(key);

    return isNumeric && isStatistical;
  });

  if (numericalColumns.length === 0) return '';

  let summary = '\n\n## 📈 Summary Statistics\n\n';

  numericalColumns.forEach((column) => {
    const values = data
      .map((row) => Number(row[column]))
      .filter((val) => !isNaN(val));
    if (values.length === 0) return;

    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    summary += `**${column}:**\n`;
    summary += `- Total: ${formatNumber(sum)}\n`;
    summary += `- Average: ${formatNumber(Math.round(avg * 100) / 100)}\n`;
    summary += `- Range: ${formatNumber(min)} - ${formatNumber(max)}\n\n`;
  });

  return summary;
}
