'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { memo, useCallback, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';

// TypeScript Enhancement: Proper relationship type definition
type RelationshipType =
  | 'head'
  | 'spouse'
  | 'child'
  | 'parent'
  | 'relative'
  | 'other';

interface Member {
  memberId: number;
  firstName: string;
  lastName: string;
  relationship: RelationshipType;
}

interface CensusDeleteMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: Member | null;
  onMemberDeleted: () => void;
}

const CensusDeleteMemberDialogComponent = ({
  open,
  onOpenChange,
  member,
  onMemberDeleted,
}: CensusDeleteMemberDialogProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSuccess, showError } = useMessage();
  const isMobile = useIsMobile();

  // Translation hooks for census context
  const t = useTranslations('census');
  const tDialogs = useTranslations('dialogs');
  const tCommon = useTranslations('common');
  const tRelationships = useTranslations('relationships');

  const handleDelete = useCallback(async () => {
    if (!member) return;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30_000); // 30s timeout

    try {
      setIsDeleting(true);

      const response = await fetch(
        `/api/census/members?id=${member.memberId}`,
        {
          method: 'DELETE',
          signal: controller.signal,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete household member');
      }

      // Use centralized alert system with census context
      showSuccess('memberDeleted');
      onOpenChange(false);
      onMemberDeleted();
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          showError('requestTimeout');
        } else if (
          error.message.includes('network') ||
          error.message.includes('fetch')
        ) {
          showError('networkError');
        } else {
          console.error('Error deleting household member:', error);
          showError('failedToDeleteHouseholdMember');
        }
      } else {
        console.error('Unknown error deleting household member:', error);
        showError('failedToDeleteHouseholdMember');
      }
    } finally {
      clearTimeout(timeoutId);
      setIsDeleting(false);
    }
  }, [member, showSuccess, showError, onOpenChange, onMemberDeleted]);

  // Performance Optimization: Memoize computed values
  const memberName = useMemo(
    () => (member ? `${member.firstName} ${member.lastName}` : ''),
    [member]
  );

  // TypeScript Enhancement: Proper type casting with validation
  const relationshipText = useMemo(() => {
    if (!member?.relationship) return '';
    try {
      return tRelationships(member.relationship as any);
    } catch {
      return member.relationship; // Fallback to raw value if translation fails
    }
  }, [member?.relationship, tRelationships]);

  // Performance Optimization: Memoized dialog content component
  const DialogContentComponent = useCallback(
    () => (
      <div className="space-y-4">
        {/* Member information display */}
        <div className="flex items-center gap-3 rounded-lg border border-muted/30 bg-muted/20 p-4">
          <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-[#FF6308]/10 to-[#97A4FF]/10">
            <User className="h-5 w-5 text-[#FF6308]" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="truncate font-medium text-foreground">{memberName}</p>
            <p className="text-muted-foreground text-sm capitalize">
              {relationshipText}
            </p>
          </div>
        </div>

        {/* Warning message */}
        <div className="flex items-start gap-3 rounded-lg border border-destructive/20 bg-destructive/5 p-4">
          <AlertTriangle className="mt-0.5 h-5 w-5 flex-shrink-0 text-destructive" />
          <div className="space-y-2">
            <p className="font-medium text-destructive">
              {tDialogs('thisWillPermanentlyDelete', { memberName })}
            </p>
            <p className="text-muted-foreground text-sm">
              {tDialogs('thisActionCannotBeUndoneMemberRemoved')}
            </p>
          </div>
        </div>
      </div>
    ),
    [memberName, relationshipText, tDialogs]
  );

  // Performance Optimization: Memoized action buttons component
  const ActionButtons = useCallback(
    () => (
      <div className="flex justify-end gap-2">
        <Button
          disabled={isDeleting}
          onClick={() => onOpenChange(false)}
          variant="outline"
        >
          {tCommon('cancel')}
        </Button>
        <Button
          className="flex items-center gap-2"
          disabled={isDeleting}
          onClick={handleDelete}
          variant="destructive"
        >
          {isDeleting ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {tCommon('deleting')}
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4" />
              {t('delete')}
            </>
          )}
        </Button>
      </div>
    ),
    [isDeleting, handleDelete, onOpenChange, tCommon, t]
  );

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and fully scrollable content including titles
  if (isMobile) {
    return (
      <Drawer onOpenChange={onOpenChange} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader className="sr-only">
            <DrawerTitle>{tDialogs('deleteMember')}</DrawerTitle>
            <DrawerDescription>{tDialogs('pleaseReviewInformationBelow')}</DrawerDescription>
          </DrawerHeader>
          <div className="scrollbar-hide flex-1 overflow-y-auto">
            <div className="flex flex-col gap-1.5 p-4">
              <h2 className="font-semibold text-lg leading-none tracking-tight">
                {tDialogs('deleteMember')}
              </h2>
              <p className="text-muted-foreground text-sm">
                {tDialogs('pleaseReviewInformationBelow')}
              </p>
            </div>
            <div className="px-4 pb-4">
              <div className="space-y-6">
                <DialogContentComponent />
              </div>

              <div className="border-t pt-4 mt-6">
                <ActionButtons />
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="font-semibold text-lg">
            {tDialogs('deleteMember')}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {tDialogs('pleaseReviewInformationBelow')}
          </DialogDescription>
        </DialogHeader>

        <DialogContentComponent />

        <DialogFooter>
          <ActionButtons />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Performance Optimization: Memoize the component to prevent unnecessary re-renders
export const CensusDeleteMemberDialog = memo(CensusDeleteMemberDialogComponent);
