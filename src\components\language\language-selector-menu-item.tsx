'use client';

import { Languages } from 'lucide-react';
import * as React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenuButton } from '@/components/ui/sidebar';
import { languages, useLanguage } from '@/contexts/LanguageContext';

export function LanguageSelectorMenuItem() {
  const { selectedLanguage, setLanguage } = useLanguage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="cursor-pointer">
        <SidebarMenuButton
          asChild
          className="cursor-pointer"
          tooltip="Language"
        >
          <button className="flex w-full cursor-pointer items-center gap-2">
            <div className="flex h-5 w-5 items-center justify-center">
              <Languages className="h-5 w-5" />
            </div>
            <span>{selectedLanguage.nativeLabel}</span>
          </button>
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languages.map((language) => (
          <DropdownMenuItem
            className="cursor-pointer"
            key={language.code}
            onClick={() => setLanguage(language)}
          >
            {language.nativeLabel}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
