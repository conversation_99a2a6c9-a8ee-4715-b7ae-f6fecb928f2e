/**
 * Helper Functions Module
 *
 * Contains general utility and helper functions for the chatbot system.
 * This module provides common operations, validation helpers, and data processing
 * functions that are used throughout the chatbot functionality.
 *
 * This module provides:
 * - Input validation and sanitization helpers
 * - Security validation functions
 * - Data processing and transformation utilities
 * - SQL injection detection
 * - String manipulation and formatting functions
 */

import type { QueryIntent } from '@/types/analytics';

/**
 * Security: Detect SQL commands and injection attempts
 * Checks for common SQL keywords that could indicate injection attempts
 */
export function containsSQLCommands(message: string): boolean {
  const sqlKeywords = [
    'select',
    'insert',
    'update',
    'delete',
    'drop',
    'create',
    'alter',
    'truncate',
    'grant',
    'revoke',
    'exec',
    'execute',
    'union',
    'join',
    'where',
    'from',
    'into',
    'values',
    'set',
    'table',
    'database',
    'schema',
    'index',
    'view',
    'procedure',
    'function',
    'trigger',
    'constraint',
    'primary',
    'foreign',
    'key',
    'references',
    'cascade',
    'restrict',
    'check',
    'default',
    'null',
    'not null',
    'unique',
    'auto_increment',
    'identity',
    'sequence',
  ];

  const lowerMessage = message.toLowerCase();

  // Check for SQL keywords with word boundaries to avoid false positives
  return sqlKeywords.some((keyword) => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'i');
    return regex.test(lowerMessage);
  });
}

/**
 * Security: Enhanced input sanitization to prevent prompt injection
 * Sanitizes user input by removing potentially dangerous content
 * RESTORED: Full original implementation with all security patterns
 */
export function sanitizeUserInput(input: string): string {
  // First, basic length and type validation
  if (typeof input !== 'string') {
    return '';
  }

  // Limit input length to prevent abuse
  if (input.length > 500) {
    input = input.substring(0, 500);
  }

  // Remove potential HTML/script injection
  input = input.replace(/[<>'"]/g, '');

  // Remove SQL keywords (case-insensitive)
  input = input.replace(
    /\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT|TRUNCATE|GRANT|REVOKE)\b/gi,
    ''
  );

  // Remove javascript protocols and event handlers
  input = input.replace(/javascript:/gi, '');
  input = input.replace(/on\w+\s*=/gi, '');

  // Advanced prompt injection patterns
  input = input.replace(
    /ignore\s+(all\s+)?(previous|prior|above|earlier)\s+(instructions?|prompts?|commands?)/gi,
    ''
  );
  input = input.replace(/forget\s+(everything|all|previous|prior)/gi, '');
  input = input.replace(/system\s*(prompt|message|instruction)/gi, '');
  input = input.replace(/you\s+are\s+(now|actually|really)/gi, '');
  input = input.replace(/pretend\s+(to\s+be|you\s+are)/gi, '');
  input = input.replace(/act\s+as\s+(if\s+you\s+are|a)/gi, '');
  input = input.replace(/role\s*:\s*system/gi, '');
  input = input.replace(/\[SYSTEM\]/gi, '');
  input = input.replace(/\{system\}/gi, '');

  // Remove potential prompt delimiters and control characters
  input = input.replace(/[{}[\]]/g, '');
  input = input.replace(/\|\|\|/g, '');
  input = input.replace(/---/g, '');
  input = input.replace(/```/g, '');

  // Remove excessive whitespace and normalize
  input = input.replace(/\s+/g, ' ').trim();

  return input;
}

/**
 * INDUSTRY STANDARD 2025: Preserve analytics data while maintaining security
 * Sanitizes database results to prevent prompt injection while preserving data integrity
 * RESTORED: Full original implementation with CHART_DATA preservation
 */
export function sanitizeDatabaseResult(result: string): string {
  if (typeof result !== 'string') {
    return '';
  }

  // Remove potential prompt injection from database results
  let sanitized = result
    .replace(/system\s*(prompt|message|instruction)/gi, '[FILTERED]')
    .replace(/ignore\s+(all\s+)?(previous|prior|above|earlier)/gi, '[FILTERED]')
    .replace(/you\s+are\s+(now|actually|really)/gi, '[FILTERED]')
    .replace(/\[SYSTEM\]/gi, '[FILTERED]')
    .replace(/\{system\}/gi, '[FILTERED]')
    .replace(/```/g, ''); // Remove code blocks

  // CRITICAL FIX: Preserve CHART_DATA and analytics results
  // If result contains CHART_DATA, allow longer length for analytics
  if (sanitized.includes('CHART_DATA:')) {
    // Allow up to 2000 characters for chart data (industry standard for analytics)
    if (sanitized.length > 2000) {
      sanitized = `${sanitized.substring(0, 2000)}...`;
    }
  } else {
    // Standard limit for non-chart data
    if (sanitized.length > 500) {
      sanitized = `${sanitized.substring(0, 500)}...`;
    }
  }

  return sanitized.trim();
}

/**
 * Security validation for intent-based queries
 * Validates that the intent contains only allowed data types and analysis types
 */
export function validateIntentSecurity(intent: QueryIntent): boolean {
  // Ensure only allowed data types (census tables only)
  const allowedDataTypes = [
    'member_demographics',
    'household_info',
    'sacrament_records',
    'census_participation',
    'temporal_analysis',
    'general',
  ];

  // Ensure only allowed analysis types
  const allowedAnalysisTypes = [
    'count',
    'distribution',
    'list',
    'overview',
    'trend',
    'comparison',
  ];

  return (
    allowedDataTypes.includes(intent.dataType) &&
    allowedAnalysisTypes.includes(intent.analysisType) &&
    intent.confidence >= 0 &&
    intent.confidence <= 1
  );
}

/**
 * Validate query message length and complexity
 * Ensures the query is within acceptable limits
 */
export function validateQueryLength(userMessage: string): boolean {
  return userMessage.length <= 500;
}

/**
 * Check if a message contains potentially harmful content
 * Combines multiple security checks for comprehensive validation
 */
export function isMessageSecure(message: string): boolean {
  // Check length
  if (!validateQueryLength(message)) {
    return false;
  }

  // Check for SQL injection
  if (containsSQLCommands(message)) {
    return false;
  }

  // Check for script injection patterns
  const scriptPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /eval\s*\(/i,
    /function\s*\(/i,
  ];

  if (scriptPatterns.some((pattern) => pattern.test(message))) {
    return false;
  }

  return true;
}

/**
 * Extract and validate sacrament type from user message
 * Returns standardized sacrament type or null if not found
 */
export function extractSacramentType(message: string): string | null {
  const lowerMessage = message.toLowerCase();

  // Map of common terms to standardized sacrament types
  const sacramentMap: Record<string, string> = {
    baptism: 'baptism',
    baptisms: 'baptism',
    baptized: 'baptism',
    baptise: 'baptism',
    baptised: 'baptism',
    confirmation: 'confirmation',
    confirmations: 'confirmation',
    confirmed: 'confirmation',
    communion: 'communion',
    communions: 'communion',
    'first communion': 'communion',
    'holy communion': 'communion',
    marriage: 'matrimony',
    marriages: 'matrimony',
    married: 'matrimony',
    wedding: 'matrimony',
    weddings: 'matrimony',
    funeral: 'funeral',
    funerals: 'funeral',
    burial: 'funeral',
    burials: 'funeral',
  };

  // Find matching sacrament type
  for (const [term, sacramentType] of Object.entries(sacramentMap)) {
    if (lowerMessage.includes(term)) {
      return sacramentType;
    }
  }

  return null;
}

/**
 * Format number with appropriate locale-specific formatting
 * Handles large numbers and provides readable output
 */
export function formatNumber(
  num: number,
  locale: 'en' | 'zh-CN' = 'en'
): string {
  try {
    return new Intl.NumberFormat(locale === 'zh-CN' ? 'zh-CN' : 'en-AU').format(
      num
    );
  } catch {
    // Fallback to simple string conversion
    return num.toString();
  }
}

/**
 * Format percentage with appropriate precision
 * Ensures consistent percentage formatting across the application
 */
export function formatPercentage(value: number, precision = 1): string {
  return `${value.toFixed(precision)}%`;
}

/**
 * Truncate text to specified length with ellipsis
 * Useful for limiting display text while preserving readability
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Clean and normalize whitespace in text
 * Removes excessive whitespace and normalizes line breaks
 */
export function normalizeWhitespace(text: string): string {
  return text
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/\n\s*\n/g, '\n') // Remove empty lines
    .trim();
}

/**
 * Check if a string represents a valid number
 * More robust than isNaN for string validation
 */
export function isValidNumber(value: string): boolean {
  return (
    !(isNaN(Number(value)) || isNaN(Number.parseFloat(value))) &&
    isFinite(Number(value))
  );
}

/**
 * Parse and validate year from user input
 * Returns valid year or null if invalid
 */
export function parseYear(input: string): number | null {
  const year = Number.parseInt(input, 10);
  const currentYear = new Date().getFullYear();

  // Validate year range (reasonable census years)
  if (year >= 1900 && year <= currentYear + 10) {
    return year;
  }

  return null;
}
