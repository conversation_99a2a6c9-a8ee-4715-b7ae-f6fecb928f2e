import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Toaster } from 'sonner';
import { RootLayoutClient } from '@/components/layout/root-layout-client';
import { ThemeProvider } from '@/components/theme/theme-provider';
import { AlertProvider } from '@/contexts/AlertContext';
import { CombinedAuthProvider } from '@/providers/combined-auth-provider';

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {

  const { locale } = await params;


  const messages = await getMessages({ locale: locale as 'en' | 'zh-CN' });

  return (
    <NextIntlClientProvider messages={messages}>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        disableTransitionOnChange
        enableSystem
      >
        <AlertProvider>
          <CombinedAuthProvider>
            <RootLayoutClient>{children}</RootLayoutClient>
          </CombinedAuthProvider>
          <Toaster position="top-right" richColors />
        </AlertProvider>
      </ThemeProvider>
    </NextIntlClientProvider>
  );
}
