'use client';

import { HelpCircle, Home, LogOut } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAdminAuth } from '@/hooks/useAdminAuth';

interface UserDropdownProps {
  userName: string;
}

export function UserDropdown({ userName }: UserDropdownProps) {
  const { signOutFromAdmin, session, isAuthenticated } = useAdminAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [displayName, setDisplayName] = useState(userName);
  const t = useTranslations('admin');
  const tNav = useTranslations('navigation');

  // Sync with client-side session when available
  useEffect(() => {
    if (isAuthenticated && session?.user?.name) {
      setDisplayName(session.user.name);
    }
  }, [session, isAuthenticated]);

  // Generate user initials from the display name
  const userInitials =
    displayName
      .split(' ')
      .map((name) => name.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2) || 'AD';

  return (
    <DropdownMenu onOpenChange={setIsOpen} open={isOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          className="group relative h-8 w-8 overflow-hidden rounded-full"
          size="sm"
          variant="ghost"
        >
          {/* Diffused gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6308] via-[#97A4FF] to-[#BDC9E6] opacity-90 transition-opacity duration-300 group-hover:opacity-100" />
          <div className="absolute inset-0 bg-gradient-to-tr from-[#FF6308]/80 via-transparent to-[#97A4FF]/60 transition-transform duration-500 group-hover:scale-110" />

          <Avatar className="relative z-10 h-8 w-8">
            <AvatarFallback className="bg-transparent font-medium text-white text-xs shadow-sm">
              {userInitials}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-56"
        forceMount
        sideOffset={8}
      >
        {/* User Info */}
        <div className="flex items-center gap-2 p-2">
          <div className="relative">
            {/* Diffused gradient background for dropdown avatar */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#FF6308] via-[#97A4FF] to-[#BDC9E6] opacity-90" />
            <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#FF6308]/80 via-transparent to-[#97A4FF]/60" />

            <Avatar className="relative z-10 h-8 w-8">
              <AvatarFallback className="bg-transparent font-medium text-white text-xs shadow-sm">
                {userInitials}
              </AvatarFallback>
            </Avatar>
          </div>
          <div className="flex flex-col space-y-1">
            <p className="font-medium text-sm leading-none">{displayName}</p>
            <p className="text-muted-foreground text-xs leading-none">
              {t('administrator')}
            </p>
          </div>
        </div>

        <DropdownMenuSeparator />

        {/* Homepage */}
        <DropdownMenuItem
          onClick={() => window.location.href = '/'}
          className="flex cursor-pointer items-center gap-2"
        >
          <Home className="h-4 w-4" />
          <span>{t('homepage')}</span>
        </DropdownMenuItem>

        {/* FAQ */}
        <DropdownMenuItem asChild>
          <Link className="flex cursor-pointer items-center gap-2" href="/help">
            <HelpCircle className="h-4 w-4" />
            <span>{tNav('faq')}</span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Logout */}
        <DropdownMenuItem
          className="flex cursor-pointer items-center gap-2 text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
          onClick={() => signOutFromAdmin()}
        >
          <LogOut className="h-4 w-4" />
          <span>{tNav('logout')}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
