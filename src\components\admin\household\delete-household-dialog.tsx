'use client';

import {
  <PERSON><PERSON>Circle,
  AlertTriangle,
  Home,
  Info,
  Trash2,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';

interface DeleteHouseholdDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  householdId: number;
  householdName: string;
  onHouseholdDeleted: () => void;
}

interface DeletionInfo {
  canDelete: boolean;
  deleteType: 'household_with_members' | 'household_head_only';
  memberCount: number;
  nonHeadMemberCount: number;
  householdHead: {
    name: string;
    id: number;
  } | null;
  warningMessage: string;
  members: Array<{
    id: number;
    name: string;
    relationship: string;
    isHead: boolean;
  }>;
}

export function DeleteHouseholdDialog({
  open,
  onOpenChange,
  householdId,
  householdName,
  onHouseholdDeleted,
}: DeleteHouseholdDialogProps) {
  const { showError } = useMessage();
  const isMobile = useIsMobile();
  const t = useTranslations('dialogs');
  const tAdmin = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tErrors = useTranslations('errors');
  const [deletionInfo, setDeletionInfo] = useState<DeletionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchDeletionInfo = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/households/${householdId}/deletion-info`
      );

      if (!response.ok) {
        throw new Error(tErrors('failedToFetchDeletionInfo'));
      }

      const data = await response.json();
      setDeletionInfo(data);
    } catch (error) {
      console.error('Error fetching deletion info:', error);
      showError('DataLoadFailed');
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }, [householdId, showError, onOpenChange, tErrors]);

  // Fetch deletion info when dialog opens
  useEffect(() => {
    if (open && householdId) {
      fetchDeletionInfo();
    }
  }, [open, householdId, fetchDeletionInfo]);

  const handleDelete = async () => {
    if (!deletionInfo?.canDelete) return;

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/admin/households/${householdId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || t('failedToDeleteHousehold'));
      }

      onOpenChange(false);
      onHouseholdDeleted();
    } catch (error) {
      console.error('Error deleting household:', error);
      showError('failedToDeleteHousehold');
    } finally {
      setIsDeleting(false);
    }
  };

  const getDialogContent = () => {
    if (loading) {
      return (
        <div className="py-6 text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground">
            {tAdmin('loadingDeletionInfo')}
          </p>
        </div>
      );
    }

    if (!deletionInfo) {
      return (
        <div className="py-6 text-center">
          <AlertCircle className="mx-auto mb-4 h-8 w-8 text-destructive" />
          <p className="text-muted-foreground">
            {tAdmin('failedToLoadDeletionInfo')}
          </p>
        </div>
      );
    }

    switch (deletionInfo.deleteType) {
      case 'household_head_only':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-950/20">
              <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="font-medium text-orange-800 dark:text-orange-200">
                  {t('deleteHousehold')}
                </p>
                <p className="text-orange-700 text-sm dark:text-orange-300">
                  This will permanently delete{' '}
                  <span className="font-medium">{householdName}</span>.
                </p>
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Home className="h-4 w-4" />
                <span>{tCommon('householdInformationWillBeDele')}</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>{tCommon('householdHeadDetailsWillBeRemo')}</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Info className="h-4 w-4" />
                <span>{tCommon('associatedUniqueCodeWillBeMark')}</span>
              </div>
            </div>
            <p className="text-muted-foreground text-sm">
              {t('thisActionCannotBeUndoneHouseholdRemoved')}
            </p>
          </div>
        );

      case 'household_with_members':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 rounded-lg border border-destructive/20 bg-destructive/10 p-4">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <div>
                <p className="font-medium text-destructive">
                  {t('cannotDeleteHousehold')}
                </p>
                <p className="text-muted-foreground text-sm">
                  <span className="font-medium">{householdName}</span> has
                  members other than the household head.
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground text-sm">
                <Users className="h-4 w-4" />
                <span>
                  {deletionInfo.memberCount} total members (
                  {deletionInfo.nonHeadMemberCount} non-head members)
                </span>
              </div>

              <div className="space-y-2">
                <p className="font-medium text-sm">
                  {tCommon('householdMembers')}:
                </p>
                <ScrollArea className="max-h-32 rounded-md border p-2">
                  <div className="space-y-1">
                    {deletionInfo.members.map((member) => (
                      <div
                        className="flex items-center justify-between text-sm"
                        key={member.id}
                      >
                        <span className={member.isHead ? 'font-medium' : ''}>
                          {member.name}
                        </span>
                        <Badge
                          className="text-xs"
                          variant={member.isHead ? 'default' : 'outline'}
                        >
                          {member.relationship}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>

            <p className="text-muted-foreground text-sm">
              {t('pleaseDeleteNonHeadMembers')}
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  const getDialogTitle = () => {
    if (loading || !deletionInfo) return t('deleteHousehold');

    switch (deletionInfo.deleteType) {
      case 'household_head_only':
        return t('deleteHousehold');
      case 'household_with_members':
        return t('cannotDeleteHousehold');
      default:
        return t('deleteHousehold');
    }
  };

  const getActionButtons = () => {
    if (loading || !deletionInfo) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
      );
    }

    if (!deletionInfo.canDelete) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('close')}
        </Button>
      );
    }

    return (
      <>
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
        <Button
          className="flex items-center gap-2"
          disabled={isDeleting}
          onClick={handleDelete}
          variant="destructive"
        >
          {isDeleting ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {tCommon('deleting')}
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4" />
              {t('deleteHousehold')}
            </>
          )}
        </Button>
      </>
    );
  };

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and fully scrollable content including titles
  if (isMobile) {
    return (
      <Drawer onOpenChange={onOpenChange} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader className="sr-only">
            <DrawerTitle>{getDialogTitle()}</DrawerTitle>
            <DrawerDescription>
              {deletionInfo?.deleteType === 'household_with_members'
                ? 'This household cannot be deleted at this time.'
                : 'Please review the information below before proceeding.'}
            </DrawerDescription>
          </DrawerHeader>
          <div className="scrollbar-hide flex-1 overflow-y-auto">
            <div className="flex flex-col gap-1.5 p-4">
              <h2 className="font-semibold text-lg leading-none tracking-tight">
                {getDialogTitle()}
              </h2>
              <p className="text-muted-foreground text-sm">
                {deletionInfo?.deleteType === 'household_with_members'
                  ? 'This household cannot be deleted at this time.'
                  : 'Please review the information below before proceeding.'}
              </p>
            </div>
            <div className="px-4 pb-4">
              <div className="space-y-6">
                {getDialogContent()}
              </div>

              <div className="pt-4 mt-6">
                <div className="flex justify-end gap-2">{getActionButtons()}</div>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            {deletionInfo?.deleteType === 'household_with_members'
              ? 'This household cannot be deleted at this time.'
              : 'Please review the information below before proceeding.'}
          </DialogDescription>
        </DialogHeader>

        {getDialogContent()}

        <DialogFooter className="flex gap-2">{getActionButtons()}</DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
