import type { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { verifyBackupCode, verifyPassword, verifyTOTP } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' },
        totp: { label: 'TOTP', type: 'text' },
      },
      async authorize(credentials) {
        if (!credentials?.username) {
          return null;
        }

        try {
          // If TOTP is provided, we're in the second step of 2FA
          if (credentials.totp) {
            // Get user from database using Prisma
            const user = await prisma.admin
              ?.findUnique({
                where: { username: credentials.username },
                select: {
                  id: true,
                  username: true,
                  fullName: true,
                  twoFactorEnabled: true,
                  twoFactorSecret: true,
                  twoFactorBackupCodes: true,
                },
              })
              .catch(() => null);

            if (!(user && user.twoFactorEnabled)) {
              return null;
            }

            // Try to verify as TOTP first
            let isValid = user.twoFactorSecret
              ? verifyTOTP(credentials.totp, user.twoFactorSecret)
              : false;

            // If TOTP verification fails, try backup codes
            if (!isValid && user.twoFactorBackupCodes) {
              const backupCodes = JSON.parse(user.twoFactorBackupCodes);
              isValid = await verifyBackupCode(credentials.totp, backupCodes);

              // If backup code is valid, remove it from the list
              if (isValid) {
                // Normalize the token
                const normalizedToken = credentials.totp
                  .replace(/\s+/g, '')
                  .toUpperCase();

                // Remove the used backup code
                const updatedBackupCodes = backupCodes.filter(
                  (code: string) => code.toUpperCase() !== normalizedToken
                );

                // Update the backup codes in the database using Prisma
                await prisma.admin
                  ?.update({
                    where: { id: user.id },
                    data: {
                      twoFactorBackupCodes: JSON.stringify(updatedBackupCodes),
                    },
                  })
                  .catch(() => null);

                // Log the use of a backup code using Prisma
                try {
                  await prisma.auditLog
                    ?.create({
                      data: {
                        userType: 'admin',
                        userId: user.id,
                        action: 'use-backup-code',
                        entityType: 'admins',
                        entityId: user.id,
                        newValues: JSON.stringify({ backup_code_used: true }),
                      },
                    })
                    .catch(() => null);
                } catch (logError) {
                  // If logging fails, silently continue for security
                }
              }
            }

            if (!isValid) {
              return null;
            }

            return {
              id: user.id.toString(),
              username: user.username,
              role: 'admin', // All users in the admins table are admins
              name: user.fullName || user.username,
            };
          }
          // First step: username/password authentication using Prisma
          const user = await prisma.admin
            ?.findUnique({
              where: { username: credentials.username },
              select: {
                id: true,
                username: true,
                password: true,
                fullName: true,
                twoFactorEnabled: true,
                twoFactorSecret: true,
              },
            })
            .catch(() => null);

          if (!user) {
            return null;
          }

          // Verify password

          // Verify password using normal verification process
          const isValidPassword = await verifyPassword(
            credentials.password || '',
            user.password
          );

          if (!isValidPassword) {
            return null;
          }

          // Check if 2FA is enabled
          if (user.twoFactorEnabled) {
            // Throw a specific error that will be caught by the client
            const error = new Error('TotpRequired');
            error.name = 'TotpRequired';
            throw error;
          }

          return {
            id: user.id.toString(),
            username: user.username,
            role: 'admin', // All users in the admins table are admins
            name: user.fullName || user.username,
          };
        } catch (error) {
          throw error;
        }
      },
    }),
  ],
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.username = user.username;
        token.role = user.role;
        token.name = user.name;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          id: token.id as string,
          code: '',
          role: token.role as string,
          name: token.name as string,
          householdId: null,
          censusYearId: '',
        };
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 8 * 60 * 60, // 8 hours (reduced from 24 hours for better security)
  },
  cookies: {
    // Use custom cookie names for admin auth to avoid conflicts with census auth
    sessionToken: {
      name: 'admin-session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    callbackUrl: {
      name: 'admin-callback-url',
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: 'admin-csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  // Use a dedicated secret key for admin authentication
  // This ensures complete separation from the census authentication system
  secret: process.env.NEXTAUTH_SECRET_ADMIN,
  debug: false, // Disable debug mode in all environments for security
};
