'use client';

import * as React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export interface TimePickerProps {
  date: Date | null;
  onTimeChange: (date: Date) => void;
  showSeconds?: boolean;
  className?: string;
}

export function TimePicker({
  date,
  onTimeChange,
  showSeconds = false,
  className,
}: TimePickerProps) {
  // State for the selected time - initialise with defaults to avoid hydration issues
  const [selectedHour, setSelectedHour] = React.useState<number>(12);
  const [selectedMinute, setSelectedMinute] = React.useState<number>(0);
  const [selectedSecond, setSelectedSecond] = React.useState<number>(0);
  const [selectedAmPm, setSelectedAmPm] = React.useState<'AM' | 'PM'>('AM');

  // Update time values when date changes (client-side only)
  React.useEffect(() => {
    if (date) {
      setSelectedHour(date.getHours() % 12 || 12);
      setSelectedMinute(date.getMinutes());
      setSelectedSecond(date.getSeconds());
      setSelectedAmPm(date.getHours() >= 12 ? 'PM' : 'AM');
    }
  }, [date]);

  // Refs for scrolling to selected time values
  const hourScrollRef = React.useRef<HTMLDivElement>(null);
  const minuteScrollRef = React.useRef<HTMLDivElement>(null);
  const secondScrollRef = React.useRef<HTMLDivElement>(null);

  // Generate arrays for hours, minutes, seconds
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);
  const minutes = Array.from({ length: 60 }, (_, i) => i);
  const seconds = Array.from({ length: 60 }, (_, i) => i);
  const ampm = ['AM', 'PM'];

  // Scroll to selected time values when component mounts
  React.useEffect(() => {
    // Use setTimeout to ensure the scroll happens after the component is fully rendered
    const scrollTimer = setTimeout(() => {
      // Scroll to selected hour
      if (hourScrollRef.current) {
        const hourElement = hourScrollRef.current.querySelector(
          `[data-hour="${selectedHour}"]`
        );
        if (hourElement) {
          hourElement.scrollIntoView({ block: 'center', behavior: 'auto' });
        }
      }

      // Scroll to selected minute
      if (minuteScrollRef.current) {
        const minuteElement = minuteScrollRef.current.querySelector(
          `[data-minute="${selectedMinute}"]`
        );
        if (minuteElement) {
          minuteElement.scrollIntoView({ block: 'center', behavior: 'auto' });
        }
      }

      // Scroll to selected second
      if (secondScrollRef.current && showSeconds) {
        const secondElement = secondScrollRef.current.querySelector(
          `[data-second="${selectedSecond}"]`
        );
        if (secondElement) {
          secondElement.scrollIntoView({ block: 'center', behavior: 'auto' });
        }
      }
    }, 100);

    return () => clearTimeout(scrollTimer);
  }, [selectedHour, selectedMinute, selectedSecond, showSeconds]);

  // Function to handle time selection
  const handleTimeSelect = (
    value: number | string,
    type: 'hour' | 'minute' | 'second' | 'ampm'
  ) => {
    if (!date) return;

    // Create a new date object to avoid direct mutation
    const newDate = new Date(date);

    // Update the internal state based on type
    switch (type) {
      case 'hour':
        setSelectedHour(value as number);
        break;
      case 'minute':
        setSelectedMinute(value as number);
        break;
      case 'second':
        setSelectedSecond(value as number);
        break;
      case 'ampm':
        setSelectedAmPm(value as 'AM' | 'PM');
        break;
    }

    // Calculate hours properly based on the type of change
    const newHour = type === 'hour' ? (value as number) : selectedHour;
    const newAmPm = type === 'ampm' ? (value as 'AM' | 'PM') : selectedAmPm;

    const newHours =
      newAmPm === 'PM' && newHour !== 12
        ? newHour + 12
        : newAmPm === 'AM' && newHour === 12
          ? 0
          : newHour;

    newDate.setHours(newHours);
    newDate.setMinutes(type === 'minute' ? (value as number) : selectedMinute);
    newDate.setSeconds(type === 'second' ? (value as number) : selectedSecond);

    // Update the date
    onTimeChange(newDate);
  };

  return (
    <div className={cn('flex justify-center gap-1 p-2', className)}>
      {/* Hours column */}
      <div className="flex flex-col items-center">
        <div className="mb-1 rounded-md bg-primary px-3 py-1 font-medium text-primary-foreground text-sm">
          {selectedHour.toString().padStart(2, '0')}
        </div>
        <ScrollArea className="datetime-scrollarea h-[200px] w-[50px] md:h-[280px]">
          <div
            className="flex flex-col items-center space-y-1 py-1"
            ref={hourScrollRef}
          >
            {hours.map((hour) => (
              <div
                className={cn(
                  'cursor-pointer rounded-md px-3 py-1 text-sm hover:bg-muted',
                  selectedHour === hour && 'bg-muted font-medium'
                )}
                data-hour={hour}
                key={hour}
                onClick={() => handleTimeSelect(hour, 'hour')}
              >
                {hour.toString().padStart(2, '0')}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Minutes column */}
      <div className="flex flex-col items-center">
        <div className="mb-1 rounded-md bg-primary px-3 py-1 font-medium text-primary-foreground text-sm">
          {selectedMinute.toString().padStart(2, '0')}
        </div>
        <ScrollArea className="datetime-scrollarea h-[200px] w-[50px] md:h-[280px]">
          <div
            className="flex flex-col items-center space-y-1 py-1"
            ref={minuteScrollRef}
          >
            {minutes.map((minute) => (
              <div
                className={cn(
                  'cursor-pointer rounded-md px-3 py-1 text-sm hover:bg-muted',
                  selectedMinute === minute && 'bg-muted font-medium'
                )}
                data-minute={minute}
                key={minute}
                onClick={() => handleTimeSelect(minute, 'minute')}
              >
                {minute.toString().padStart(2, '0')}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Seconds column (optional) */}
      {showSeconds && (
        <div className="flex flex-col items-center">
          <div className="mb-1 rounded-md bg-primary px-3 py-1 font-medium text-primary-foreground text-sm">
            {selectedSecond.toString().padStart(2, '0')}
          </div>
          <ScrollArea className="datetime-scrollarea h-[200px] w-[50px] md:h-[280px]">
            <div
              className="flex flex-col items-center space-y-1 py-1"
              ref={secondScrollRef}
            >
              {seconds.map((second) => (
                <div
                  className={cn(
                    'cursor-pointer rounded-md px-3 py-1 text-sm hover:bg-muted',
                    selectedSecond === second && 'bg-muted font-medium'
                  )}
                  data-second={second}
                  key={second}
                  onClick={() => handleTimeSelect(second, 'second')}
                >
                  {second.toString().padStart(2, '0')}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* AM/PM column */}
      <div className="flex flex-col items-center">
        <div className="mb-1 rounded-md bg-primary px-3 py-1 font-medium text-primary-foreground text-sm">
          {selectedAmPm}
        </div>
        <ScrollArea className="datetime-scrollarea h-[200px] w-[50px] md:h-[280px]">
          <div className="flex flex-col items-center space-y-1 py-1">
            {ampm.map((value) => (
              <div
                className={cn(
                  'cursor-pointer rounded-md px-3 py-1 text-sm hover:bg-muted',
                  selectedAmPm === value && 'bg-muted font-medium'
                )}
                key={value}
                onClick={() => handleTimeSelect(value, 'ampm')}
              >
                {value}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
