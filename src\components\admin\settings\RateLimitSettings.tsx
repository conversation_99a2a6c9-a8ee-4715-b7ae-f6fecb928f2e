'use client';

import { <PERSON><PERSON><PERSON>riangle, <PERSON>, Shield } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { FormFieldWithTooltip } from '@/components/admin/settings/FormFieldWithTooltip';
import { SettingsCard } from '@/components/admin/settings/SettingsCard';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useFormSubmit } from '@/hooks/useFormSubmit';
import { useMessage } from '@/hooks/useMessage';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientRateLimitFormValues,
  createClientRateLimitSchema,
} from '@/lib/validation/client/settings-client';

export function RateLimitSettings() {
  const { showSuccess, showError, showWarning } = useMessage();
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tValidation = useTranslations('validation');
  const tErrors = useTranslations('errors');

  // Create client schema with translations
  const rateLimitSchema = createClientRateLimitSchema(tValidation);

  // Initialize form with validation
  const form = useForm<ClientRateLimitFormValues>({
    resolver: zodResolver(rateLimitSchema),
    defaultValues: {
      maxAttempts: 5,
      lockoutMinutes: 15,
      escalationMinutes: 15,
    },
  });

  // Watch form values for UI updates
  const maxAttempts = form.watch('maxAttempts');
  const lockoutMinutes = form.watch('lockoutMinutes');
  const escalationMinutes = form.watch('escalationMinutes');

  // Fetch rate limit settings from the API
  useEffect(() => {
    const fetchRateLimitSettings = async () => {
      try {
        const response = await fetch('/api/settings/rate-limiting');

        // Handle API errors
        if (!response.ok) {
          console.warn(`API call failed with status ${response.status}`);

          // If unauthorized, it means we're not logged in
          if (response.status === 401) {
            console.warn('Not logged in as admin');
            showWarning('needToBeLoggedInAsAdmin');
          } else {
            showError('failedToLoadRateLimitSettings');
          }

          // Set default values
          form.reset({
            maxAttempts: 5,
            lockoutMinutes: 15,
            escalationMinutes: 15,
          });
          return;
        }

        let data;
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError);
          showError('InvalidServerResponse');
          // Set default values
          form.reset({
            maxAttempts: 5,
            lockoutMinutes: 15,
            escalationMinutes: 15,
          });
          return;
        }

        // Update form values with validation
        form.reset({
          maxAttempts: Number(data.maxAttempts) || 5,
          lockoutMinutes: Number(data.lockoutMinutes) || 15,
          escalationMinutes: Number(data.escalationMinutes) || 15,
        });
      } catch (error) {
        console.error('Error fetching rate limit settings:', error);
        // For development purposes, use default values if the API call fails
        form.reset({
          maxAttempts: 5,
          lockoutMinutes: 15,
          escalationMinutes: 15,
        });
        showWarning('usingDefaultValuesDatabaseUnavailable');
      }
    };

    fetchRateLimitSettings();
  }, [form, showError, showWarning, t]);

  // Use our custom hook for form submission
  const { handleSubmit: submitRateLimitSettings, isSubmitting } =
    useFormSubmit<ClientRateLimitFormValues>({
      onSubmit: async (data) => {
        try {
          // Add a timeout to the fetch request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10_000); // 10 second timeout

          let response;
          let responseData;

          try {
            response = await fetch('/api/settings/rate-limiting', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            // Check if the response is valid before trying to parse JSON
            if (!response.ok && response.status !== 400) {
              throw new Error(
                tErrors('serverRespondedWithStatusRespo', {
                  status: response.status.toString(),
                })
              );
            }

            try {
              responseData = await response.json();
            } catch {
              throw new Error(tErrors('invalidJsonResponseFromServer'));
            }
          } catch (fetchError) {
            clearTimeout(timeoutId);
            console.error('Fetch error:', fetchError);

            // Handle network errors
            if (
              fetchError &&
              typeof fetchError === 'object' &&
              'name' in fetchError &&
              fetchError.name === 'AbortError'
            ) {
              throw new Error(t('requestTimedOut'));
            }

            if (
              fetchError &&
              typeof fetchError === 'object' &&
              'message' in fetchError &&
              typeof fetchError.message === 'string' &&
              fetchError.message.includes('Failed to fetch')
            ) {
              throw new Error(t('networkErrorCheckConnection'));
            }

            throw fetchError;
          }

          // Now we can safely use response and responseData
          if (!response.ok) {
            // If unauthorized, return a specific message
            if (response.status === 401) {
              return {
                success: false,
                message: tCommon('youNeedToBeLoggedInAsAnAdminTo'),
              };
            }

            if (responseData.details) {
              // Handle validation errors
              showError('InvalidInput', 'settings');
              return {
                success: false,
                message: tCommon('pleaseCheckTheFormForErrors'),
              };
            }

            showError('UpdateFailed', 'settings');
            return {
              success: false,
              message: tCommon('failedToUpdateRateLimitSetting'),
            };
          }

          // Check if this is a development message about database connection
          if (
            responseData.message &&
            responseData.message.includes('would be saved in production')
          ) {
            return {
              success: true,
              message: responseData.message,
            };
          }

          // Success - use centralized alert system
          showSuccess('rateLimitSettingsUpdated');
          return {
            success: true,
            suppressAlert: true,
          };
        } catch (error: unknown) {
          console.error('Error updating rate limit settings:', error);

          if (error instanceof Error && error.message === 'ValidationError') {
            showError('InvalidInput', 'settings');
          } else {
            showError('UpdateFailed', 'settings');
          }

          return {
            success: false,
            suppressAlert: true,
          };
        }
      },
    });

  // Calculate escalation pattern for display
  const getEscalationPattern = () => {
    // Ensure we have valid numbers to prevent NaN
    const baseLockout = Number(lockoutMinutes) || 15;
    const escalation = Number(escalationMinutes) || 15;

    const pattern = [];
    for (let escalationLevel = 0; escalationLevel < 4; escalationLevel++) {
      const duration = baseLockout + escalation * escalationLevel;
      const ordinal =
        escalationLevel === 0
          ? '1st'
          : escalationLevel === 1
            ? '2nd'
            : escalationLevel === 2
              ? '3rd'
              : '4th';
      pattern.push(`${ordinal}: ${duration}min`);
    }
    return pattern.join(' → ');
  };

  return (
    <SettingsCard
      description={t('configureSecurityThresholds')}
      form={form}
      isSubmitting={isSubmitting}
      onFormSubmit={submitRateLimitSettings}
      statusIndicator={
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground text-sm">
            {t('censusAttemptsLockout', {
              attempts: (Number(maxAttempts) || 5).toString(),
              minutes: (Number(lockoutMinutes) || 15).toString(),
            })}
          </span>
        </div>
      }
      title={t('censusAuthenticationRateLimiting')}
    >
      <div className="space-y-6">
        <Alert className="border bg-muted/50">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{t('securityNotice')}</AlertTitle>
          <AlertDescription className="mt-2">
            <p className="text-sm">
              {t('theseSettingsControlCensusResponse')}
              {t('adminAuthProtectedBy2FA')}
            </p>
            <p className="mt-2 text-amber-600 text-sm">
              <strong>{tCommon('important')}:</strong>{' '}
              {t('changesApplyToNewLockoutsOnly')}
            </p>
          </AlertDescription>
        </Alert>

        <div className="grid gap-6 sm:grid-cols-3">
          <FormFieldWithTooltip
            error={form.formState.errors.maxAttempts}
            helperText={t('configureMaximumFailedAttempts')}
            id="maxAttempts"
            label={t('maximumAttempts')}
            placeholder="5"
            register={form.register}
            required
            tooltip={t('numberOfFailedCensusAttempts')}
            type="number"
          />

          <FormFieldWithTooltip
            error={form.formState.errors.lockoutMinutes}
            helperText={t('configureInitialLockoutDuration')}
            id="lockoutMinutes"
            label={t('initialLockoutMinutes')}
            placeholder="15"
            register={form.register}
            required
            tooltip={t('durationOfFirstLockout')}
            type="number"
          />

          <FormFieldWithTooltip
            error={form.formState.errors.escalationMinutes}
            helperText={t('setToZeroToDisableEscalation')}
            id="escalationMinutes"
            label={t('escalationIncrementMinutes')}
            placeholder="15"
            register={form.register}
            required
            tooltip={t('additionalMinutesForSubsequent')}
            type="number"
          />
        </div>

        {/* Escalation Pattern Preview */}
        <div className="mt-6 rounded-md border bg-muted/50 p-4">
          <div className="mb-2 flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium text-sm">
              {tCommon('censusLockoutPatternPreview')}
            </span>
          </div>
          <p className="text-muted-foreground text-sm">
            {t('censusLockoutDurations')}: {getEscalationPattern()}
          </p>
          {Number(escalationMinutes) === 0 && (
            <p className="mt-1 text-amber-600 text-xs">
              {t('escalationDisabledAllLockouts', {
                minutes: (Number(lockoutMinutes) || 15).toString(),
              })}
            </p>
          )}
        </div>
      </div>
    </SettingsCard>
  );
}
