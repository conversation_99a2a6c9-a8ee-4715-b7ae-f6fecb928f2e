'use client';

import { AlertCircle, Calendar, Home, Phone, Trash2, User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { MemberSacramentsDisplay } from '@/components/admin/household/member-sacraments-display';
import { DeleteMemberDialog } from '@/components/admin/members/delete-member-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';
import { Skeleton } from '@/components/ui/skeleton';
import { StatusBadge } from '@/components/ui/status-badge';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';
import { formatDate } from '@/lib/utils/date-time';
import {
  getGenderBadgeColor,
  getRelationshipBadgeVariant,
} from '@/lib/utils/relationship-badge';

interface ViewMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  memberId: number;
  onMemberDeleted: () => void;
}

interface MemberDetails {
  memberId: number;
  firstName: string | null;
  lastName: string | null;
  dateOfBirth: string | null;
  age: number;
  gender: 'male' | 'female' | 'other' | null;
  mobilePhone: string | null;
  hobby?: string | null;
  occupation?: string | null;
  relationship:
    | 'head'
    | 'spouse'
    | 'child'
    | 'parent'
    | 'relative'
    | 'other'
    | null;
  householdId: number | null;
  suburb: string | null;
  census_year: number | null;
  censusYearId: number | null;
  household_head_name?: string | null;
  household_head_contact?: string | null;
  unique_code?: string | null;
  createdAt: string;
  updatedAt: string;
  sacrament_details: Array<{
    id: number;
    sacramentTypeId: number;
    sacrament_name: string;
    sacrament_code: string;
    date: string | null;
    place: string | null;
    notes: string | null;
  }>;
}

export function ViewMemberDialog({
  open,
  onOpenChange,
  memberId,
  onMemberDeleted,
}: ViewMemberDialogProps) {
  const { showError } = useMessage();
  const isMobile = useIsMobile();
  const [memberDetails, setMemberDetails] = useState<MemberDetails | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tDialogs = useTranslations('dialogs');
  const tCensus = useTranslations('census');
  const tForms = useTranslations('forms');
  const tNavigation = useTranslations('navigation');

  const fetchMemberDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/members/${memberId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || tCommon('failedToFetchMemberDetails')
        );
      }

      const data = await response.json();
      setMemberDetails(data);
    } catch (error) {
      console.error('Error fetching member details:', error);
      showError('failedToFetchMemberDetails');
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }, [memberId, showError, onOpenChange, tCommon]);

  // Fetch member details
  useEffect(() => {
    if (open && memberId) {
      fetchMemberDetails();
    }
  }, [open, memberId, fetchMemberDetails]);

  // Handle member deletion
  const handleDeleteMember = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleMemberDeleted = () => {
    setIsDeleteDialogOpen(false);
    onOpenChange(false);
    onMemberDeleted();
  };

  // Shared content component for both mobile and desktop
  const DialogContentComponent = () => {
    if (loading) {
      return (
        <div className="space-y-6 py-4">
          {/* Skeleton for member information */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-40" />
            <div className="grid grid-cols-4 gap-4">
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-16" />
                <Skeleton className="mt-1 h-4 w-24" />
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-20" />
                <Skeleton className="mt-1 h-4 w-16" />
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-16" />
                <Skeleton className="mt-1 h-4 w-20" />
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-24" />
                <Skeleton className="mt-1 h-4 w-28" />
              </div>
            </div>
          </div>

          {/* Skeleton for household information */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-48" />
            <Skeleton className="h-20 w-full" />
          </div>

          {/* Skeleton for sacraments */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-40 w-full" />
          </div>
        </div>
      );
    }

    if (memberDetails) {
      return (
        <div className="space-y-6">
          {/* Basic member information */}
          <div>
            <h3 className="mb-2 font-medium text-sm">
              {tCensus('memberInformation')}
            </h3>
            <div className="grid grid-cols-4 gap-4">
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="flex items-center gap-1 font-medium text-muted-foreground text-xs">
                  <User className="h-3 w-3" />
                  {tCommon('name')}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {memberDetails.firstName || tCommon('unknown')}{' '}
                  {memberDetails.lastName || tCommon('unknown')}
                </p>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="flex items-center gap-1 font-medium text-muted-foreground text-xs">
                  <Calendar className="h-3 w-3" />
                  {t('ageDob')}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {memberDetails.age} {t('years')}
                </p>
                <p className="text-muted-foreground text-xs">
                  {memberDetails.dateOfBirth
                    ? formatDate(new Date(memberDetails.dateOfBirth))
                    : t('notSpecified')}
                </p>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="font-medium text-muted-foreground text-xs">
                  {tCommon('gender')}
                </h4>
                <div className="mt-1">
                  <Badge className={getGenderBadgeColor(memberDetails.gender)}>
                    {memberDetails.gender
                      ? memberDetails.gender.charAt(0).toUpperCase() +
                        memberDetails.gender.slice(1)
                      : t('notSpecified')}
                  </Badge>
                </div>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="flex items-center gap-1 font-medium text-muted-foreground text-xs">
                  <Phone className="h-3 w-3" />
                  {tCommon('mobile')}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {memberDetails.mobilePhone || tCommon('notProvided')}
                </p>
              </div>
            </div>

            {/* Hobby and Occupation */}
            {(memberDetails.hobby || memberDetails.occupation) && (
              <div className="mt-4 grid grid-cols-2 gap-4">
                <div className="rounded-md bg-muted/20 p-4">
                  <h4 className="font-medium text-muted-foreground text-xs">
                    {tForms('hobby')}
                  </h4>
                  <p className="mt-1 font-medium text-sm">
                    {memberDetails.hobby || tCommon('notSpecified')}
                  </p>
                </div>
                <div className="rounded-md bg-muted/20 p-4">
                  <h4 className="font-medium text-muted-foreground text-xs">
                    {tForms('occupation')}
                  </h4>
                  <p className="mt-1 font-medium text-sm">
                    {memberDetails.occupation || tCommon('notSpecified')}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Household information */}
          <div>
            <h3 className="mb-2 font-medium text-sm">
              {tCommon('householdInformation')}
            </h3>
            <div className="rounded-md bg-muted/10 p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="flex items-center gap-1 font-medium text-muted-foreground text-xs">
                    <Home className="h-3 w-3" />
                    {tNavigation('household')}
                  </h4>
                  <p className="mt-1 font-medium text-sm">
                    {memberDetails.suburb || tCommon('unknown')}{' '}
                    {memberDetails.householdId
                      ? `(#${memberDetails.householdId})`
                      : ''}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-muted-foreground text-xs">
                    {tCommon('censusYear')}
                  </h4>
                  <p className="mt-1 font-medium text-sm">
                    {memberDetails.census_year || tCommon('unknown')}
                  </p>
                </div>
              </div>

              {memberDetails.household_head_name &&
                memberDetails.relationship !== 'head' && (
                  <div className="mt-3 border-muted/30 border-t pt-3">
                    <h4 className="font-medium text-muted-foreground text-xs">
                      {t('householdHead')}
                    </h4>
                    <p className="mt-1 font-medium text-sm">
                      {memberDetails.household_head_name}
                    </p>
                    {memberDetails.household_head_contact && (
                      <p className="text-muted-foreground text-xs">
                        {memberDetails.household_head_contact}
                      </p>
                    )}
                  </div>
                )}
            </div>
          </div>

          {/* Sacrament Details Section */}
          <MemberSacramentsDisplay
            memberId={memberDetails.memberId}
            memberName={`${memberDetails.firstName || tCommon('unknown')} ${memberDetails.lastName || tCommon('unknown')}`}
          />
        </div>
      );
    }

    return (
      <div className="py-8 text-center">
        <AlertCircle className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground">
          {tCommon('failedToLoadMemberDetails')}
        </p>
      </div>
    );
  };

  // Action buttons component for both mobile and desktop
  const ActionButtons = () => (
    <Button
      className="flex items-center gap-2"
      onClick={handleDeleteMember}
      variant="destructive"
    >
      <Trash2 className="h-4 w-4" />
      {tDialogs('deleteMember')}
    </Button>
  );

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and fully scrollable content including titles
  if (isMobile) {
    return (
      <>
        <Drawer onOpenChange={onOpenChange} open={open}>
          <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
            <DrawerHeader className="sr-only">
              <DrawerTitle>{t('memberDetails')}</DrawerTitle>
              <DrawerDescription>
                {memberDetails ? (
                  <>
                    {memberDetails.unique_code ? (
                      <>
                        {t('householdCode')}{' '}
                        <span className="font-medium">
                          {memberDetails.unique_code}
                        </span>{' '}
                        •{' '}
                      </>
                    ) : null}
                    {tCommon('id')}:{' '}
                    <span className="font-medium">
                      {memberDetails.memberId}
                    </span>
                    {' • '}
                    <StatusBadge
                      variant={getRelationshipBadgeVariant(
                        memberDetails.relationship
                      )}
                    >
                      {memberDetails.relationship
                        ? memberDetails.relationship.charAt(0).toUpperCase() +
                          memberDetails.relationship.slice(1)
                        : tCommon('notSpecified')}
                    </StatusBadge>
                  </>
                ) : (
                  t('loadingMemberInfo')
                )}
              </DrawerDescription>
            </DrawerHeader>
            <div className="scrollbar-hide flex-1 overflow-y-auto">
              <div className="flex flex-col gap-1.5 p-4">
                <h2 className="font-semibold text-lg leading-none tracking-tight">
                  {t('memberDetails')}
                </h2>
                <div className="text-muted-foreground text-sm">
                  {memberDetails ? (
                    <>
                      {memberDetails.unique_code ? (
                        <>
                          {t('householdCode')}{' '}
                          <span className="font-medium">
                            {memberDetails.unique_code}
                          </span>{' '}
                          •{' '}
                        </>
                      ) : null}
                      {tCommon('id')}:{' '}
                      <span className="font-medium">
                        {memberDetails.memberId}
                      </span>
                      {' • '}
                      <StatusBadge
                        variant={getRelationshipBadgeVariant(
                          memberDetails.relationship
                        )}
                      >
                        {memberDetails.relationship
                          ? memberDetails.relationship.charAt(0).toUpperCase() +
                            memberDetails.relationship.slice(1)
                          : tCommon('notSpecified')}
                      </StatusBadge>
                    </>
                  ) : (
                    t('loadingMemberInfo')
                  )}
                </div>
              </div>
              <div className="px-4 pb-4">
                <div className="space-y-6">
                  <DialogContentComponent />
                </div>

                <div className="pt-4 mt-6">
                  <div className="flex justify-end">
                    <ActionButtons />
                  </div>
                </div>
              </div>
            </div>
          </DrawerContent>
        </Drawer>

        {/* Enhanced Delete Dialog */}
        {memberDetails && (
          <DeleteMemberDialog
            memberId={memberDetails.memberId}
            memberName={`${memberDetails.firstName || tCommon('unknown')} ${memberDetails.lastName || tCommon('unknown')}`}
            onMemberDeleted={handleMemberDeleted}
            onOpenChange={setIsDeleteDialogOpen}
            open={isDeleteDialogOpen}
          />
        )}
      </>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <>
      <Dialog onOpenChange={onOpenChange} open={open}>
        <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>{t('memberDetails')}</DialogTitle>
            <DialogDescription>
              {memberDetails ? (
                <>
                  {memberDetails.unique_code ? (
                    <>
                      {t('householdCode')}{' '}
                      <span className="font-medium">
                        {memberDetails.unique_code}
                      </span>{' '}
                      •{' '}
                    </>
                  ) : null}
                  {tCommon('id')}:{' '}
                  <span className="font-medium">{memberDetails.memberId}</span>
                  {' • '}
                  <StatusBadge
                    variant={getRelationshipBadgeVariant(
                      memberDetails.relationship
                    )}
                  >
                    {memberDetails.relationship
                      ? memberDetails.relationship.charAt(0).toUpperCase() +
                        memberDetails.relationship.slice(1)
                      : tCommon('notSpecified')}
                  </StatusBadge>
                </>
              ) : (
                t('loadingMemberInfo')
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogContentComponent />

          <div className="flex justify-end border-t pt-4">
            <ActionButtons />
          </div>
        </DialogContent>
      </Dialog>

      {/* Enhanced Delete Dialog */}
      {memberDetails && (
        <DeleteMemberDialog
          memberId={memberDetails.memberId}
          memberName={`${memberDetails.firstName || tCommon('unknown')} ${memberDetails.lastName || tCommon('unknown')}`}
          onMemberDeleted={handleMemberDeleted}
          onOpenChange={setIsDeleteDialogOpen}
          open={isDeleteDialogOpen}
        />
      )}
    </>
  );
}
