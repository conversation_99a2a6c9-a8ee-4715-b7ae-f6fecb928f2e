import type { Metada<PERSON> } from 'next';
import { getServerSession } from 'next-auth/next';
import { getTranslations } from 'next-intl/server';
import { CensusHeader } from '@/components/census/census-header';
import { SessionMonitor } from '@/components/census/session-monitor';
import { TourProvider } from '@/components/tour/TourProvider';
import { TourSystem } from '@/components/tour/TourSystem';
import { CensusProgressProvider } from '@/hooks/use-census-progress';
import { censusAuthOptions } from '@/lib/census-auth/census-auth-options';

// Force dynamic rendering to ensure fresh session data
export const dynamic = 'force-dynamic';

// Define metadata for census section
export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('metadata');

  return {
    title: t('censusFormTitle'),
    description: t('censusFormDescription'),
  };
}

export default async function CensusLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  let isSecondStage = false;

  try {
    // Get session server-side for second stage detection
    const session = await getServerSession(censusAuthOptions);

    // Determine second stage server-side (main logic)
    const isAuthenticated = !!session?.user;
    const hasHouseholdId = !!session?.user?.householdId;

    // Server-side logic: authenticated and has household ID
    // Account page exclusion will be handled client-side in header
    isSecondStage = isAuthenticated && hasHouseholdId;
  } catch (_error) {
    // Log error in development, fail silently in production
    if (process.env.NODE_ENV === 'development') {
    }
    // Default to false for safety
    isSecondStage = false;
  }

  return (
    <TourProvider>
      <CensusProgressProvider>
        <div className="flex flex-col">

          <SessionMonitor />


          <CensusHeader isSecondStage={isSecondStage} />


          <main>{children}</main>


        </div>


        <TourSystem />
      </CensusProgressProvider>
    </TourProvider>
  );
}
