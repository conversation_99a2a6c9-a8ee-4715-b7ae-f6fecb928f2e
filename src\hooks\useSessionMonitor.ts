'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';

import { useCensusAuth } from '@/hooks/useCensusAuth';

/**
 * Hook to monitor census session validity and handle automatic logout
 * when household is deleted by admin
 *
 * This hook is specifically designed for the census authentication system
 * and does not interfere with the admin authentication system.
 */
export function useSessionMonitor() {
  const { session, status, signOutFromCensus } = useCensusAuth();
  const router = useRouter();

  const hasShownAlert = useRef(false);

  useEffect(() => {
    // Only monitor authenticated census sessions (household role)
    // This ensures we don't interfere with admin authentication
    if (status === 'loading' || !session || session.user.role !== 'household') {
      return;
    }

    // Additional safety check: ensure this is a census session with required properties
    if (!session.user.censusYearId || typeof session.user.id !== 'string') {
      return;
    }

    // Check if session is still valid by making a request to the session API
    const checkSessionValidity = async () => {
      try {
        const response = await fetch('/api/census/session', {
          method: 'GET',
          credentials: 'include',
        });

        if (!response.ok) {
          // Session is invalid, handle logout
          if (!hasShownAlert.current) {
            hasShownAlert.current = true;

            if (response.status === 403) {
              // Account was deleted - redirect using the toast-redirect mechanism for clean URLs
              window.location.href =
                '/api/census/auth/toast-redirect?reason=account_deleted&redirectTo=/';
            } else {
              // Other session issues - use standard logout
              await signOutFromCensus();
            }
          }
        }
      } catch (error) {
        console.error('Error checking session validity:', error);
        // Don't show alerts for network errors to avoid spam
      }
    };

    // Check session validity immediately
    checkSessionValidity();

    // Set up periodic session validation (every 30 seconds)
    const intervalId = setInterval(checkSessionValidity, 30_000);

    // Cleanup interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [session, status, router, signOutFromCensus]);

  return {
    isMonitoring:
      status === 'authenticated' && session?.user?.role === 'household',
  };
}
