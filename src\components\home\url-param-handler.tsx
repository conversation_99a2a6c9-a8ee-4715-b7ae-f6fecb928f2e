'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useMessage } from '@/hooks/useMessage';

/**
 * Component that handles URL parameters and converts them to toast notifications
 * This is used to handle legacy URL parameters like /?reason=unauthenticated
 * Now uses the centralized message system with automatic translation
 */
export function UrlParamHandler() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { showError } = useMessage();

  useEffect(() => {

    const reason = searchParams?.get('reason');

    if (reason) {

      showError(reason, 'census');


      router.replace('/');
    }
  }, [searchParams, router, showError]);


  return null;
}
