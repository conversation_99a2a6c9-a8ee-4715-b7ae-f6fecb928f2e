'use client';

import { useTranslations } from 'next-intl';
import { memo, useCallback, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { SuburbAutocompleteOptimized } from '@/components/census/suburb-autocomplete-optimized';
import { Form } from '@/components/forms/common/Form';
import { FormField } from '@/components/forms/common/FormField';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { useMessage } from '@/hooks/useMessage';
import { formatDateForDatabase } from '@/lib/utils/date-time';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientHouseholdRegistrationFormValues,
  createClientHouseholdRegistrationSchema,
} from '@/lib/validation/client/census-client';

interface HouseholdRegistrationFormProps {
  code: string;
  onRegistrationComplete: (householdId: string) => void;
}

const HouseholdRegistrationFormComponent = ({
  code,
  onRegistrationComplete,
}: HouseholdRegistrationFormProps) => {
  const t = useTranslations('census');
  const tForms = useTranslations('forms');
  const tCommon = useTranslations('common');
  const tErrors = useTranslations('errors');
  const tValidation = useTranslations('validation');

  const { showSuccess, showError } = useMessage();
  const { session } = useCensusAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create client-side validation schema with translations
  const householdRegistrationSchema = createClientHouseholdRegistrationSchema(
    (key: string) => tValidation(key as any)
  );

  const form = useForm<ClientHouseholdRegistrationFormValues>({
    resolver: zodResolver(householdRegistrationSchema),
    defaultValues: {
      suburb: '',
      headFirstName: '',
      headLastName: '',
      headDateOfBirth: undefined,
      mobilePhone: '',
      gender: undefined,
    },
  });

  const onSubmit = useCallback(
    async (data: ClientHouseholdRegistrationFormValues) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30_000); // 30s timeout

      try {
        setIsSubmitting(true);

        // Format the date properly for the API (preserving local date to prevent timezone shifts)
        const formattedData = {
          ...data,
          headDateOfBirth:
            data.headDateOfBirth instanceof Date
              ? formatDateForDatabase(data.headDateOfBirth)
              : data.headDateOfBirth,
        };

        // Step 1: Register the household
        const registerResponse = await fetch('/api/census/register-household', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formattedData,
            uniqueCodeId: session?.user?.id,
            censusYearId: session?.user?.censusYearId,
          }),
          signal: controller.signal,
        });

        let registerResult;
        try {
          registerResult = await registerResponse.json();
        } catch (jsonError) {
          console.error('Failed to parse registration response:', jsonError);
          throw new Error(tErrors('invalidServerResponse'));
        }

        if (!registerResponse.ok) {
          const errorMessage =
            registerResult.error || tErrors('failedToRegisterHousehold');
          const errorDetails = registerResult.details
            ? `: ${registerResult.details}`
            : '';
          throw new Error(`${errorMessage}${errorDetails}`);
        }

        if (!registerResult.success) {
          const errorMessage =
            registerResult.error || tErrors('failedToRegisterHousehold');
          const errorDetails = registerResult.details
            ? `: ${registerResult.details}`
            : '';
          throw new Error(`${errorMessage}${errorDetails}`);
        }

        // Extract the household ID from the result
        if (!registerResult.householdId) {
          throw new Error(tErrors('noHouseholdIdReturned'));
        }
        const householdId = registerResult.householdId.toString();

        // Step 2: Update the session through the dedicated API endpoint
        try {
          const sessionUpdateResponse = await fetch(
            '/api/census/auth/update-session',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                householdId,
                name: data.headLastName, // Update name to household head's last name
              }),
            }
          );

          if (sessionUpdateResponse.ok) {
            try {
              const result = await sessionUpdateResponse.json();
              console.log('Session update result:', result);
            } catch (jsonError) {
              console.warn(
                'Failed to parse session update success response:',
                jsonError
              );
            }
          } else {
            let errorMessage = tErrors(
              'sessionUpdateFailedButRegistrationSuccessful'
            );
            try {
              const errorData = await sessionUpdateResponse.json();
              if (errorData.error) {
                errorMessage = `${tErrors('sessionUpdateFailed')}: ${errorData.error}`;
              }
            } catch (jsonError) {
              console.warn(
                'Failed to parse session update error response:',
                jsonError
              );
            }
            console.warn(errorMessage);
            // Continue with the process even if session update fails
            // The session will be updated on the next page refresh
          }
        } catch (fetchError) {
          console.warn('Failed to call session update endpoint:', fetchError);
          // Continue with the process even if session update fails
          // The session will be updated on the next page refresh
        }

        // Step 3: Update the parent component and show success message
        // Notify parent component that registration is complete
        // This updates the parent's state to show the main form instead of registration form
        onRegistrationComplete(householdId);

        // Show success message
        showSuccess('householdRegistered');

        // The database has been updated, and the JWT token will be updated on the next refresh
        // through the enhanced JWT callback we implemented
        console.log('Registration complete, showing main form');
      } catch (error) {
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            showError('requestTimeout');
          } else if (
            error.message.includes('network') ||
            error.message.includes('fetch')
          ) {
            showError('networkError');
          } else {
            console.error('Registration error:', error);
            showError('registrationError');
          }
        } else {
          console.error('Unknown registration error:', error);
          showError('registrationError');
        }
      } finally {
        clearTimeout(timeoutId);
        setIsSubmitting(false);
      }
    },
    [
      session?.user?.id,
      session?.user?.censusYearId,
      onRegistrationComplete,
      showSuccess,
      showError,
      tErrors,
    ]
  );

  return (
    <div className="mx-auto w-full max-w-2xl">
      {/* Header Section */}
      <div className="mb-8">
        <h1 className="font-bold text-3xl tracking-tight">
          {t('setUpYourHousehold')}
        </h1>
        <p className="mt-2 text-muted-foreground">{t('welcomeSetupMessage')}</p>
      </div>

      {/* Unique Code Display */}
      <div className="mb-8 rounded-lg border border-primary/10 bg-primary/5 p-4">
        <p className="text-sm">
          {t('yourUniqueCodeIs')}{' '}
          <span className="font-bold font-mono">{code}</span>{' '}
          {t('keepCodeSafe')}
        </p>
      </div>

      <Separator className="my-6" />

      {/* Form Section */}
      <div className="mb-6">
        <h2 className="mb-2 font-semibold text-xl">
          {t('householdInformation')}
        </h2>
        <p className="text-muted-foreground text-sm">
          {t('enterDetailsToRegister')}
        </p>
      </div>

      <Form
        className="space-y-6"
        form={form}
        isLoading={isSubmitting}
        onSubmit={onSubmit}
        submitText={t('registerHousehold')}
      >
        {/* Location Section */}
        <div className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="sm:col-span-2">
              <SuburbAutocompleteOptimized
                control={form.control}
                error={form.formState.errors.suburb}
                label={tForms('suburb')}
                name="suburb"
                placeholder={tForms('searchSuburbPlaceholder')}
                required
              />
            </div>
          </div>
        </div>

        {/* Household Head Section */}
        <div className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            <FormField
              error={form.formState.errors.headFirstName}
              id="headFirstName"
              label={tForms('householdHeadFirstName')}
              placeholder={tForms('enterFirstNamePlaceholder')}
              register={form.register}
              required
            />

            <FormField
              error={form.formState.errors.headLastName}
              id="headLastName"
              label={tForms('householdHeadLastName')}
              placeholder={tForms('enterLastNamePlaceholder')}
              register={form.register}
              required
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            {/* Mobile Phone - Full width on mobile, half width on desktop */}
            <FormField
              error={form.formState.errors.mobilePhone}
              id="mobilePhone"
              label={tForms('mobilePhone')}
              placeholder={tForms('enterMobileNumberPlaceholder')}
              register={form.register}
              required
            />

            {/* Date of Birth + Gender - Nested grid for side-by-side layout */}
            <div className="grid grid-cols-2 gap-4">
              {/* Date of Birth */}
              <div className="space-y-2">
                <Label
                  className="font-medium text-sm"
                  htmlFor="headDateOfBirth"
                >
                  {tForms('householdHeadDateOfBirth')}
                  <span className="ml-1 text-destructive">*</span>
                </Label>
                <Controller
                  control={form.control}
                  name="headDateOfBirth"
                  render={({ field }) => (
                    <DatePicker
                      className={
                        form.formState.errors.headDateOfBirth
                          ? 'border-destructive'
                          : ''
                      }
                      date={field.value instanceof Date ? field.value : null}
                      placeholderText={tForms('selectDateOfBirthPlaceholder')}
                      preventFutureDates={true}
                      setDate={(date) => field.onChange(date)}
                    />
                  )}
                />
                {form.formState.errors.headDateOfBirth && (
                  <p className="text-destructive text-xs">
                    {form.formState.errors.headDateOfBirth.message}
                  </p>
                )}
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Label className="font-medium text-sm" htmlFor="gender">
                  {tForms('gender')}
                  <span className="ml-1 text-destructive">*</span>
                </Label>
                <Select
                  onValueChange={(value) => {
                    form.setValue(
                      'gender',
                      value as 'male' | 'female' | 'other',
                      {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true,
                      }
                    );
                  }}
                  value={form.getValues('gender')}
                >
                  <SelectTrigger
                    className={
                      form.formState.errors.gender ? 'border-destructive' : ''
                    }
                    id="gender"
                  >
                    <SelectValue
                      placeholder={tForms('selectGenderPlaceholder')}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">{tForms('male')}</SelectItem>
                    <SelectItem value="female">{tForms('female')}</SelectItem>
                    <SelectItem value="other">{tForms('other')}</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.gender && (
                  <p className="text-destructive text-xs">
                    {form.formState.errors.gender.message}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const HouseholdRegistrationForm = memo(
  HouseholdRegistrationFormComponent
);
