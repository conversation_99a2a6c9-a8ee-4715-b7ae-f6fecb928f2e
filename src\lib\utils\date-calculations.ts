/**
 * Date Calculation Utilities
 * Replaces MySQL TIMESTAMPDIFF and other date functions with TypeScript implementations
 */

import {
  differenceInDays,
  differenceInMonths,
  differenceInYears,
  format,
  parseISO,
} from 'date-fns';

/**
 * Calculate age in years (replaces TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()))
 */
export function calculateAge(dateOfBirth: Date | string | null): number | null {
  if (!dateOfBirth) return null;

  try {
    const birthDate =
      typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth;
    return differenceInYears(new Date(), birthDate);
  } catch (error) {
    console.error('Error calculating age:', error);
    return null;
  }
}

/**
 * Calculate age at a specific date
 */
export function calculateAgeAtDate(
  dateOfBirth: Date | string | null,
  targetDate: Date | string
): number | null {
  if (!dateOfBirth) return null;

  try {
    const birthDate =
      typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth;
    const target =
      typeof targetDate === 'string' ? parseISO(targetDate) : targetDate;
    return differenceInYears(target, birthDate);
  } catch (error) {
    console.error('Error calculating age at date:', error);
    return null;
  }
}

/**
 * Get age group category (used in demographics)
 */
export function getAgeGroup(
  dateOfBirth: Date | string | null,
  censusYear?: number
): string {
  const age = censusYear
    ? calculateAgeAtDate(dateOfBirth, new Date(censusYear, 11, 31)) // December 31st of census year
    : calculateAge(dateOfBirth);

  if (age === null) return 'Unknown';

  if (age < 18) return 'Under 18';
  if (age >= 18 && age <= 30) return '18-30';
  if (age >= 31 && age <= 50) return '31-50';
  if (age >= 51 && age <= 70) return '51-70';
  return 'Over 70';
}

/**
 * Calculate months between dates (replaces TIMESTAMPDIFF(MONTH, ...))
 */
export function calculateMonthsDifference(
  startDate: Date | string,
  endDate: Date | string
): number {
  try {
    const start =
      typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    return differenceInMonths(end, start);
  } catch (error) {
    console.error('Error calculating months difference:', error);
    return 0;
  }
}

/**
 * Calculate days between dates (replaces TIMESTAMPDIFF(DAY, ...))
 */
export function calculateDaysDifference(
  startDate: Date | string,
  endDate: Date | string
): number {
  try {
    const start =
      typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    return differenceInDays(end, start);
  } catch (error) {
    console.error('Error calculating days difference:', error);
    return 0;
  }
}

/**
 * Format date for display (replaces MySQL DATE_FORMAT)
 */
export function formatDate(
  date: Date | string | null,
  formatString = 'yyyy-MM-dd'
): string | null {
  if (!date) return null;

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, formatString);
  } catch (error) {
    console.error('Error formatting date:', error);
    return null;
  }
}

/**
 * Get current date (replaces CURDATE())
 */
export function getCurrentDate(): Date {
  return new Date();
}

/**
 * Get current timestamp (replaces NOW())
 */
export function getCurrentTimestamp(): Date {
  return new Date();
}

/**
 * Add/subtract time from date (replaces DATE_ADD/DATE_SUB)
 */
export function addTime(
  date: Date | string,
  amount: number,
  unit: 'years' | 'months' | 'days' | 'hours' | 'minutes'
): Date {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const result = new Date(dateObj);

  switch (unit) {
    case 'years':
      result.setFullYear(result.getFullYear() + amount);
      break;
    case 'months':
      result.setMonth(result.getMonth() + amount);
      break;
    case 'days':
      result.setDate(result.getDate() + amount);
      break;
    case 'hours':
      result.setHours(result.getHours() + amount);
      break;
    case 'minutes':
      result.setMinutes(result.getMinutes() + amount);
      break;
  }

  return result;
}

/**
 * Check if date is within range
 */
export function isDateInRange(
  date: Date | string,
  startDate: Date | string,
  endDate: Date | string
): boolean {
  try {
    const checkDate = typeof date === 'string' ? parseISO(date) : date;
    const start =
      typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;

    return checkDate >= start && checkDate <= end;
  } catch (error) {
    console.error('Error checking date range:', error);
    return false;
  }
}

/**
 * Get age statistics for a group of members
 */
export function getAgeStatistics(
  members: Array<{ dateOfBirth: Date | string | null }>,
  censusYear?: number
) {
  const ages = members
    .map((member) =>
      censusYear
        ? calculateAgeAtDate(member.dateOfBirth, new Date(censusYear, 11, 31))
        : calculateAge(member.dateOfBirth)
    )
    .filter((age): age is number => age !== null);

  if (ages.length === 0) {
    return {
      count: 0,
      averageAge: 0,
      minAge: 0,
      maxAge: 0,
      ageGroups: {},
    };
  }

  const ageGroups = members.reduce(
    (groups, member) => {
      const ageGroup = getAgeGroup(member.dateOfBirth, censusYear);
      groups[ageGroup] = (groups[ageGroup] || 0) + 1;
      return groups;
    },
    {} as Record<string, number>
  );

  return {
    count: ages.length,
    averageAge: Math.round(
      ages.reduce((sum, age) => sum + age, 0) / ages.length
    ),
    minAge: Math.min(...ages),
    maxAge: Math.max(...ages),
    ageGroups,
  };
}

/**
 * Convert MySQL date string to JavaScript Date
 */
export function parseMySQLDate(mysqlDate: string | null): Date | null {
  if (!mysqlDate) return null;

  try {
    // Handle MySQL datetime format: YYYY-MM-DD HH:MM:SS
    if (mysqlDate.includes(' ')) {
      return parseISO(mysqlDate.replace(' ', 'T'));
    }
    // Handle MySQL date format: YYYY-MM-DD
    return parseISO(mysqlDate);
  } catch (error) {
    console.error('Error parsing MySQL date:', error);
    return null;
  }
}

/**
 * Format date for PostgreSQL insertion
 */
export function formatForPostgreSQL(date: Date | string | null): string | null {
  if (!date) return null;

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return dateObj.toISOString();
  } catch (error) {
    console.error('Error formatting date for PostgreSQL:', error);
    return null;
  }
}
